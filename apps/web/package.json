{"name": "@kesar-mango/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:production": "NODE_ENV=production next build", "start": "next start -p 3001", "start:production": "NODE_ENV=production next start -p 3001", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf .next", "clean:all": "rm -rf .next node_modules package-lock.json && npm install", "export": "next export", "sitemap": "next-sitemap", "security-check": "npm audit", "performance-check": "lighthouse http://localhost:3001 --output=json --output-path=./lighthouse-report.json"}, "dependencies": {"next": "14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.5", "zustand": "^4.4.7", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-intersection-observer": "^9.5.3"}, "devDependencies": {"typescript": "^5.3.2", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/jest": "^29.5.8", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-security": "^1.7.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "webpack-bundle-analyzer": "^4.9.1", "lighthouse": "^11.3.0", "next-sitemap": "^4.2.3", "prettier": "^3.1.0", "@svgr/webpack": "^8.1.0"}}