import { Sport, Event, CasinoGame, Bet, UserStats, PlatformStats } from '@/types'

// Mock Sports Data
export const mockSports: Sport[] = [
  {
    id: '1',
    name: 'Football',
    slug: 'football',
    icon: '⚽',
    isActive: true,
    eventCount: 156,
  },
  {
    id: '2',
    name: 'Basketball',
    slug: 'basketball',
    icon: '🏀',
    isActive: true,
    eventCount: 89,
  },
  {
    id: '3',
    name: 'Tennis',
    slug: 'tennis',
    icon: '🎾',
    isActive: true,
    eventCount: 234,
  },
  {
    id: '4',
    name: 'Cricket',
    slug: 'cricket',
    icon: '🏏',
    isActive: true,
    eventCount: 45,
  },
  {
    id: '5',
    name: 'Ice Hockey',
    slug: 'ice-hockey',
    icon: '🏒',
    isActive: true,
    eventCount: 67,
  },
  {
    id: '6',
    name: 'Baseball',
    slug: 'baseball',
    icon: '⚾',
    isActive: true,
    eventCount: 123,
  },
]

// Mock Events Data
export const mockEvents: Event[] = [
  {
    id: '1',
    leagueId: '1',
    homeTeam: { id: '1', name: 'Manchester United', logo: '/teams/man-utd.png' },
    awayTeam: { id: '2', name: 'Liverpool', logo: '/teams/liverpool.png' },
    startTime: '2024-01-15T20:00:00.000Z', // Fixed time to prevent hydration issues
    status: 'scheduled',
    isLive: false,
    markets: [
      {
        id: '1',
        eventId: '1',
        name: 'Match Winner',
        type: 'match_winner',
        isActive: true,
        isSuspended: false,
        outcomes: [
          { id: '1', marketId: '1', name: 'Manchester United', odds: 2.45, isActive: true, trend: 'up' },
          { id: '2', marketId: '1', name: 'Draw', odds: 3.20, isActive: true, trend: 'neutral' },
          { id: '3', marketId: '1', name: 'Liverpool', odds: 2.80, isActive: true, trend: 'down' },
        ],
      },
      {
        id: '2',
        eventId: '1',
        name: 'Total Goals',
        type: 'over_under',
        isActive: true,
        isSuspended: false,
        outcomes: [
          { id: '4', marketId: '2', name: 'Over 2.5', odds: 1.85, isActive: true, trend: 'up' },
          { id: '5', marketId: '2', name: 'Under 2.5', odds: 1.95, isActive: true, trend: 'down' },
        ],
      },
    ],
  },
  {
    id: '2',
    leagueId: '1',
    homeTeam: { id: '3', name: 'Chelsea', logo: '/teams/chelsea.png' },
    awayTeam: { id: '4', name: 'Arsenal', logo: '/teams/arsenal.png' },
    startTime: '2024-01-15T18:00:00.000Z', // Fixed time for live event
    status: 'live',
    isLive: true,
    homeScore: 1,
    awayScore: 2,
    minute: 67,
    period: '2nd Half',
    markets: [
      {
        id: '3',
        eventId: '2',
        name: 'Match Winner',
        type: 'match_winner',
        isActive: true,
        isSuspended: false,
        outcomes: [
          { id: '6', marketId: '3', name: 'Chelsea', odds: 4.50, isActive: true, trend: 'up' },
          { id: '7', marketId: '3', name: 'Draw', odds: 3.80, isActive: true, trend: 'neutral' },
          { id: '8', marketId: '3', name: 'Arsenal', odds: 1.65, isActive: true, trend: 'down' },
        ],
      },
    ],
  },
  {
    id: '3',
    leagueId: '2',
    homeTeam: { id: '5', name: 'Lakers', logo: '/teams/lakers.png' },
    awayTeam: { id: '6', name: 'Warriors', logo: '/teams/warriors.png' },
    startTime: '2024-01-15T22:00:00.000Z', // Fixed time to prevent hydration issues
    status: 'scheduled',
    isLive: false,
    markets: [
      {
        id: '4',
        eventId: '3',
        name: 'Match Winner',
        type: 'match_winner',
        isActive: true,
        isSuspended: false,
        outcomes: [
          { id: '9', marketId: '4', name: 'Lakers', odds: 1.95, isActive: true, trend: 'neutral' },
          { id: '10', marketId: '4', name: 'Warriors', odds: 1.85, isActive: true, trend: 'up' },
        ],
      },
    ],
  },
]

// Mock Casino Games
export const mockCasinoGames: CasinoGame[] = [
  {
    id: '1',
    name: 'Mega Moolah',
    provider: 'Microgaming',
    category: 'Slots',
    thumbnail: '/games/mega-moolah.jpg',
    isLive: false,
    minBet: 0.25,
    maxBet: 6.25,
    rtp: 88.12,
    popularity: 95,
    isNew: false,
    isFeatured: true,
  },
  {
    id: '2',
    name: 'Live Blackjack',
    provider: 'Evolution Gaming',
    category: 'Live Casino',
    thumbnail: '/games/live-blackjack.jpg',
    isLive: true,
    minBet: 5,
    maxBet: 5000,
    rtp: 99.28,
    popularity: 88,
    isNew: false,
    isFeatured: true,
  },
  {
    id: '3',
    name: 'Starburst',
    provider: 'NetEnt',
    category: 'Slots',
    thumbnail: '/games/starburst.jpg',
    isLive: false,
    minBet: 0.10,
    maxBet: 100,
    rtp: 96.09,
    popularity: 92,
    isNew: false,
    isFeatured: false,
  },
  {
    id: '4',
    name: 'Lightning Roulette',
    provider: 'Evolution Gaming',
    category: 'Live Casino',
    thumbnail: '/games/lightning-roulette.jpg',
    isLive: true,
    minBet: 0.20,
    maxBet: 20000,
    rtp: 97.30,
    popularity: 90,
    isNew: true,
    isFeatured: true,
  },
]

// Mock User Stats
export const mockUserStats: UserStats = {
  totalBets: 1247,
  totalWins: 623,
  totalLosses: 624,
  winRate: 49.96,
  totalStaked: 12450.00,
  totalWon: 13890.50,
  netProfit: 1440.50,
  biggestWin: 2850.00,
  currentStreak: 3,
  longestStreak: 12,
}

// Mock Platform Stats
export const mockPlatformStats: PlatformStats = {
  totalUsers: 125847,
  totalBets: 2847593,
  totalVolume: 45892347.50,
  liveEvents: 156,
  popularSports: mockSports.slice(0, 3),
  biggestWins: [
    {
      id: '1',
      userId: 'user1',
      type: 'combo',
      selections: [],
      stake: 50,
      potentialWin: 15750,
      totalOdds: 315,
      status: 'won',
      placedAt: '2024-01-15T16:00:00.000Z',
      settledAt: '2024-01-15T17:00:00.000Z',
    },
    {
      id: '2',
      userId: 'user2',
      type: 'single',
      selections: [],
      stake: 1000,
      potentialWin: 8500,
      totalOdds: 8.5,
      status: 'won',
      placedAt: '2024-01-15T12:00:00.000Z',
      settledAt: '2024-01-15T13:00:00.000Z',
    },
  ],
}

// Mock trending bets
export const mockTrendingBets = [
  {
    event: 'Manchester United vs Liverpool',
    selection: 'Over 2.5 Goals',
    odds: 1.85,
    percentage: 78,
  },
  {
    event: 'Lakers vs Warriors',
    selection: 'Lakers to Win',
    odds: 1.95,
    percentage: 65,
  },
  {
    event: 'Chelsea vs Arsenal',
    selection: 'Arsenal to Win',
    odds: 1.65,
    percentage: 82,
  },
]

// Mock recent winners
export const mockRecentWinners = [
  {
    username: 'BetMaster2023',
    amount: 15750,
    game: 'Football Combo',
    time: '2 minutes ago',
  },
  {
    username: 'LuckyPlayer',
    amount: 8500,
    game: 'Live Blackjack',
    time: '5 minutes ago',
  },
  {
    username: 'SportsFan',
    amount: 3200,
    game: 'Basketball',
    time: '8 minutes ago',
  },
]
