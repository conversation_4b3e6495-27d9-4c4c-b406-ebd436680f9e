/**
 * Data Service Layer
 * Replaces mock data with real API calls in production
 */

import { env, isProduction } from '@/lib/env'
import { sportsApi, casinoApi, apiClient } from '@/lib/api-config'
import { mockSports, mockEvents, mockCasinoGames, mockTrendingBets } from '@/data/mock-data'
import type { Sport, Event, CasinoGame, Bet } from '@/types'

// Sports Data Service
export class SportsDataService {
  async getSports(): Promise<Sport[]> {
    if (!isProduction && env.MOCK_SPORTS_DATA) {
      return mockSports
    }

    try {
      const response = await sportsApi.getSports()
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch sports')
    } catch (error) {
      console.error('Error fetching sports:', error)
      // Fallback to mock data in case of API failure
      return mockSports
    }
  }

  async getEvents(sportId?: string): Promise<Event[]> {
    if (!isProduction && env.MOCK_SPORTS_DATA) {
      return sportId 
        ? mockEvents.filter(event => event.sport.id === sportId)
        : mockEvents
    }

    try {
      const response = await sportsApi.getEvents(sportId)
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch events')
    } catch (error) {
      console.error('Error fetching events:', error)
      return sportId 
        ? mockEvents.filter(event => event.sport.id === sportId)
        : mockEvents
    }
  }

  async getLiveEvents(): Promise<Event[]> {
    if (!isProduction && env.MOCK_SPORTS_DATA) {
      return mockEvents.filter(event => event.isLive)
    }

    try {
      const response = await sportsApi.getLiveEvents()
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch live events')
    } catch (error) {
      console.error('Error fetching live events:', error)
      return mockEvents.filter(event => event.isLive)
    }
  }

  async getOdds(eventId: string): Promise<any> {
    if (!isProduction && env.MOCK_SPORTS_DATA) {
      const event = mockEvents.find(e => e.id === eventId)
      return event?.markets || []
    }

    try {
      const response = await sportsApi.getOdds(eventId)
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch odds')
    } catch (error) {
      console.error('Error fetching odds:', error)
      const event = mockEvents.find(e => e.id === eventId)
      return event?.markets || []
    }
  }

  async getTrendingBets(): Promise<Bet[]> {
    if (!isProduction && env.MOCK_SPORTS_DATA) {
      return mockTrendingBets
    }

    try {
      const response = await apiClient.get('/sports/trending')
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch trending bets')
    } catch (error) {
      console.error('Error fetching trending bets:', error)
      return mockTrendingBets
    }
  }
}

// Casino Data Service
export class CasinoDataService {
  async getGames(category?: string): Promise<CasinoGame[]> {
    if (!isProduction && env.MOCK_SPORTS_DATA) {
      return category 
        ? mockCasinoGames.filter(game => game.category === category)
        : mockCasinoGames
    }

    try {
      const response = await casinoApi.getGames(category)
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch casino games')
    } catch (error) {
      console.error('Error fetching casino games:', error)
      return category 
        ? mockCasinoGames.filter(game => game.category === category)
        : mockCasinoGames
    }
  }

  async getProviders(): Promise<any[]> {
    if (!isProduction && env.MOCK_SPORTS_DATA) {
      return [
        { id: '1', name: 'Evolution Gaming', logo: '/providers/evolution.png' },
        { id: '2', name: 'Pragmatic Play', logo: '/providers/pragmatic.png' },
        { id: '3', name: 'NetEnt', logo: '/providers/netent.png' },
      ]
    }

    try {
      const response = await casinoApi.getProviders()
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch providers')
    } catch (error) {
      console.error('Error fetching providers:', error)
      return []
    }
  }

  async launchGame(gameId: string): Promise<{ url: string }> {
    if (!isProduction && env.MOCK_SPORTS_DATA) {
      return { url: `/casino/demo/${gameId}` }
    }

    try {
      const response = await casinoApi.launchGame(gameId)
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to launch game')
    } catch (error) {
      console.error('Error launching game:', error)
      throw error
    }
  }
}

// User Data Service
export class UserDataService {
  async getProfile(): Promise<any> {
    try {
      const response = await apiClient.get('/user/profile')
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch profile')
    } catch (error) {
      console.error('Error fetching profile:', error)
      // Return mock user data for development
      return {
        id: '1',
        username: 'BetMaster',
        email: '<EMAIL>',
        balance: 1250.50,
        currency: 'USD',
        verified: true,
      }
    }
  }

  async getBalance(): Promise<{ balance: number; currency: string }> {
    try {
      const response = await apiClient.get('/user/balance')
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch balance')
    } catch (error) {
      console.error('Error fetching balance:', error)
      return { balance: 1250.50, currency: 'USD' }
    }
  }

  async getTransactions(): Promise<any[]> {
    try {
      const response = await apiClient.get('/user/transactions')
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch transactions')
    } catch (error) {
      console.error('Error fetching transactions:', error)
      return []
    }
  }
}

// Platform Stats Service
export class PlatformStatsService {
  async getStats(): Promise<any> {
    try {
      const response = await apiClient.get('/platform/stats')
      if (response.success && response.data) {
        return response.data
      }
      throw new Error(response.error || 'Failed to fetch platform stats')
    } catch (error) {
      console.error('Error fetching platform stats:', error)
      // Return mock stats for development
      return {
        totalUsers: 125847,
        totalVolume: 45900000,
        totalBets: 2800000,
        liveEvents: 156,
      }
    }
  }
}

// Create service instances
export const sportsDataService = new SportsDataService()
export const casinoDataService = new CasinoDataService()
export const userDataService = new UserDataService()
export const platformStatsService = new PlatformStatsService()

// Export all services
export const dataServices = {
  sports: sportsDataService,
  casino: casinoDataService,
  user: userDataService,
  platform: platformStatsService,
}

export default dataServices
