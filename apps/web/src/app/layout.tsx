import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Kesar Mango - Premium Sports & Casino Betting',
  description: 'Experience the ultimate betting platform with live sports betting, casino games, and real-time odds. Join thousands of winners today!',
  keywords: 'sports betting, casino games, live betting, online gambling, odds, poker, slots',
  authors: [{ name: 'Kesar Mango Team' }],
  creator: 'Kesar Mango',
  publisher: 'Kesar Mango',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://kesarmango.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Kesar Mango - Premium Sports & Casino Betting',
    description: 'Experience the ultimate betting platform with live sports betting, casino games, and real-time odds.',
    url: 'https://kesarmango.com',
    siteName: 'Kesar Mango',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Kesar Mango Betting Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kesar Mango - Premium Sports & Casino Betting',
    description: 'Experience the ultimate betting platform with live sports betting, casino games, and real-time odds.',
    images: ['/og-image.jpg'],
    creator: '@kesarmango',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <style dangerouslySetInnerHTML={{
          __html: `
            body {
              background-color: #0f172a !important;
              margin: 0;
              padding: 0;
            }
            #__next {
              background-color: #0f172a;
            }
          `
        }} />
      </head>
      <body className={`${inter.className} bg-dark-950 text-white min-h-screen`}>
        <Providers>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1e293b',
                color: '#fff',
                border: '1px solid #475569',
              },
              success: {
                iconTheme: {
                  primary: '#22c55e',
                  secondary: '#fff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  )
}
