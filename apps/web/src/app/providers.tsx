'use client'

import { ReactNode } from 'react'
import { BettingProvider } from '@/store/betting-store'
import { AuthProvider } from '@/store/auth-store'
import { LoadingProvider } from '@/components/providers/LoadingProvider'

interface ProvidersProps {
  children: ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <LoadingProvider>
      <AuthProvider>
        <BettingProvider>
          {children}
        </BettingProvider>
      </AuthProvider>
    </LoadingProvider>
  )
}
