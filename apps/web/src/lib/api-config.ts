/**
 * API Configuration Service
 * Centralized API configuration with proper error handling and retry logic
 */

import { env, isProduction } from './env'

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// API Configuration
export const apiConfig = {
  baseURL: env.API_URL,
  timeout: 10000,
  retries: 3,
  retryDelay: 1000,
}

// API Endpoints
export const endpoints = {
  // Authentication
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    verify: '/auth/verify',
  },
  
  // User Management
  user: {
    profile: '/user/profile',
    balance: '/user/balance',
    transactions: '/user/transactions',
    settings: '/user/settings',
    kyc: '/user/kyc',
  },
  
  // Sports Betting
  sports: {
    list: '/sports',
    events: '/sports/events',
    odds: '/sports/odds',
    live: '/sports/live',
    bet: '/sports/bet',
    history: '/sports/history',
  },
  
  // Casino
  casino: {
    games: '/casino/games',
    providers: '/casino/providers',
    play: '/casino/play',
    history: '/casino/history',
  },
  
  // Payments
  payments: {
    deposit: '/payments/deposit',
    withdraw: '/payments/withdraw',
    methods: '/payments/methods',
    history: '/payments/history',
    verify: '/payments/verify',
  },
  
  // Admin
  admin: {
    users: '/admin/users',
    bets: '/admin/bets',
    transactions: '/admin/transactions',
    reports: '/admin/reports',
    settings: '/admin/settings',
  },
} as const

// HTTP Client Configuration
export class ApiClient {
  private baseURL: string
  private timeout: number
  private retries: number
  private retryDelay: number

  constructor(config = apiConfig) {
    this.baseURL = config.baseURL
    this.timeout = config.timeout
    this.retries = config.retries
    this.retryDelay = config.retryDelay
  }

  // Generic request method with retry logic
  async request<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<ApiResponse<T>> {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.timeout)

      const response = await fetch(`${this.baseURL}${endpoint}`, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders(),
          ...options.headers,
        },
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return { success: true, data }

    } catch (error) {
      // Retry logic for network errors
      if (retryCount < this.retries && this.shouldRetry(error)) {
        await this.delay(this.retryDelay * (retryCount + 1))
        return this.request<T>(endpoint, options, retryCount + 1)
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // Authentication headers
  private getAuthHeaders(): Record<string, string> {
    const token = this.getAuthToken()
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  // Get auth token from storage
  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('auth_token')
  }

  // Retry logic
  private shouldRetry(error: any): boolean {
    // Retry on network errors, timeouts, and 5xx status codes
    return (
      error.name === 'AbortError' ||
      error.message.includes('fetch') ||
      error.message.includes('5')
    )
  }

  // Delay utility
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Create singleton instance
export const apiClient = new ApiClient()

// Specific API services
export const authApi = {
  login: (credentials: { email: string; password: string }) =>
    apiClient.post(endpoints.auth.login, credentials),
  
  register: (userData: { email: string; password: string; name: string }) =>
    apiClient.post(endpoints.auth.register, userData),
  
  logout: () => apiClient.post(endpoints.auth.logout),
  
  refreshToken: () => apiClient.post(endpoints.auth.refresh),
}

export const sportsApi = {
  getSports: () => apiClient.get(endpoints.sports.list),
  
  getEvents: (sportId?: string) =>
    apiClient.get(`${endpoints.sports.events}${sportId ? `?sport=${sportId}` : ''}`),
  
  getLiveEvents: () => apiClient.get(endpoints.sports.live),
  
  getOdds: (eventId: string) =>
    apiClient.get(`${endpoints.sports.odds}/${eventId}`),
  
  placeBet: (betData: any) => apiClient.post(endpoints.sports.bet, betData),
}

export const casinoApi = {
  getGames: (category?: string) =>
    apiClient.get(`${endpoints.casino.games}${category ? `?category=${category}` : ''}`),
  
  getProviders: () => apiClient.get(endpoints.casino.providers),
  
  launchGame: (gameId: string) =>
    apiClient.post(`${endpoints.casino.play}/${gameId}`),
}

export const paymentsApi = {
  getMethods: () => apiClient.get(endpoints.payments.methods),
  
  deposit: (amount: number, method: string) =>
    apiClient.post(endpoints.payments.deposit, { amount, method }),
  
  withdraw: (amount: number, method: string) =>
    apiClient.post(endpoints.payments.withdraw, { amount, method }),
  
  getHistory: () => apiClient.get(endpoints.payments.history),
}

// Error handling utilities
export const handleApiError = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message
  }
  
  if (error?.message) {
    return error.message
  }
  
  return 'An unexpected error occurred'
}

// Development helpers
export const mockApiResponse = <T>(data: T, delay = 1000): Promise<ApiResponse<T>> => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({ success: true, data })
    }, delay)
  })
}

// Use mock responses in development if enabled
export const useMockApi = env.MOCK_SPORTS_DATA && !isProduction
