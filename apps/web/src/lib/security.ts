/**
 * Security Configuration and Utilities
 * Production-ready security measures for betting platform
 */

import { env } from './env'

// Rate limiting configuration
export const rateLimitConfig = {
  windowMs: env.RATE_LIMIT.WINDOW, // 15 minutes
  max: env.RATE_LIMIT.MAX, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
}

// CORS configuration
export const corsConfig = {
  origin: env.NODE_ENV === 'production' 
    ? [env.APP_URL, 'https://kesarmango.com', 'https://www.kesarmango.com']
    : ['http://localhost:3001', 'http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200,
}

// Content Security Policy
export const cspConfig = {
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: [
      "'self'",
      "'unsafe-eval'",
      "'unsafe-inline'",
      'https://www.google-analytics.com',
      'https://www.googletagmanager.com',
      'https://js.stripe.com',
    ],
    styleSrc: [
      "'self'",
      "'unsafe-inline'",
      'https://fonts.googleapis.com',
    ],
    fontSrc: [
      "'self'",
      'https://fonts.gstatic.com',
    ],
    imgSrc: [
      "'self'",
      'data:',
      'https:',
      'blob:',
    ],
    connectSrc: [
      "'self'",
      'wss:',
      'https:',
      'ws:',
      env.API_URL,
    ],
    frameSrc: [
      "'self'",
      'https://www.youtube.com',
      'https://player.vimeo.com',
      'https://js.stripe.com',
    ],
    objectSrc: ["'none'"],
    baseUri: ["'self'"],
    formAction: ["'self'"],
    frameAncestors: ["'none'"],
  },
}

// Input sanitization
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

// Email validation
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 254
}

// Password strength validation
export function validatePassword(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  }
}

// JWT token validation
export function validateJWT(token: string): boolean {
  if (!token) return false
  
  const parts = token.split('.')
  if (parts.length !== 3) return false
  
  try {
    // Basic structure validation
    JSON.parse(atob(parts[1]))
    return true
  } catch {
    return false
  }
}

// Session management
export class SessionManager {
  private static readonly SESSION_KEY = 'betting_session'
  private static readonly TOKEN_KEY = 'auth_token'
  
  static setSession(token: string, expiresIn: number): void {
    if (typeof window === 'undefined') return
    
    const session = {
      token,
      expiresAt: Date.now() + expiresIn,
    }
    
    localStorage.setItem(this.SESSION_KEY, JSON.stringify(session))
    localStorage.setItem(this.TOKEN_KEY, token)
  }
  
  static getSession(): { token: string; expiresAt: number } | null {
    if (typeof window === 'undefined') return null
    
    try {
      const session = localStorage.getItem(this.SESSION_KEY)
      if (!session) return null
      
      const parsed = JSON.parse(session)
      
      // Check if session is expired
      if (Date.now() > parsed.expiresAt) {
        this.clearSession()
        return null
      }
      
      return parsed
    } catch {
      this.clearSession()
      return null
    }
  }
  
  static clearSession(): void {
    if (typeof window === 'undefined') return
    
    localStorage.removeItem(this.SESSION_KEY)
    localStorage.removeItem(this.TOKEN_KEY)
  }
  
  static isSessionValid(): boolean {
    const session = this.getSession()
    return session !== null && Date.now() < session.expiresAt
  }
}

// CSRF protection
export function generateCSRFToken(): string {
  const array = new Uint8Array(32)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

// XSS protection
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
}

// SQL injection prevention (for query building)
export function escapeSqlString(str: string): string {
  return str.replace(/'/g, "''")
}

// Secure random string generation
export function generateSecureRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const array = new Uint8Array(length)
  crypto.getRandomValues(array)
  
  return Array.from(array, byte => chars[byte % chars.length]).join('')
}

// IP address validation
export function isValidIP(ip: string): boolean {
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip)
}

// Betting-specific security
export class BettingSecurityManager {
  // Validate bet amount
  static validateBetAmount(amount: number, userBalance: number): {
    isValid: boolean
    error?: string
  } {
    if (amount <= 0) {
      return { isValid: false, error: 'Bet amount must be positive' }
    }
    
    if (amount > userBalance) {
      return { isValid: false, error: 'Insufficient balance' }
    }
    
    if (amount < 0.01) {
      return { isValid: false, error: 'Minimum bet amount is $0.01' }
    }
    
    if (amount > 10000) {
      return { isValid: false, error: 'Maximum bet amount is $10,000' }
    }
    
    return { isValid: true }
  }
  
  // Validate odds
  static validateOdds(odds: number): boolean {
    return odds > 1 && odds <= 1000 && Number.isFinite(odds)
  }
  
  // Check for suspicious betting patterns
  static checkSuspiciousBetting(bets: any[]): boolean {
    // Implement logic to detect suspicious patterns
    // This is a simplified example
    const recentBets = bets.filter(bet => 
      Date.now() - new Date(bet.timestamp).getTime() < 60000 // Last minute
    )
    
    return recentBets.length > 10 // More than 10 bets in a minute
  }
}

export default {
  rateLimitConfig,
  corsConfig,
  cspConfig,
  sanitizeInput,
  isValidEmail,
  validatePassword,
  validateJWT,
  SessionManager,
  generateCSRFToken,
  escapeHtml,
  BettingSecurityManager,
}
