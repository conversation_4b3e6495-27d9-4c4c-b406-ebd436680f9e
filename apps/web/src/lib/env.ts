/**
 * Environment Variables Configuration
 * Centralized configuration for all environment variables with validation
 */

// Validate required environment variables
function validateEnv() {
  const requiredVars = [
    'NEXT_PUBLIC_APP_NAME',
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_API_URL',
  ]

  const missing = requiredVars.filter(varName => !process.env[varName])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

// Validate environment variables in production
if (process.env.NODE_ENV === 'production') {
  validateEnv()
}

// Application Configuration
export const env = {
  // App Info
  APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Kesar Mango',
  APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001',
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // API Configuration
  API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  API_SECRET: process.env.API_SECRET_KEY || '',
  JWT_SECRET: process.env.JWT_SECRET || 'dev-jwt-secret',
  
  // Database
  DATABASE_URL: process.env.DATABASE_URL || '',
  REDIS_URL: process.env.REDIS_URL || '',
  
  // Payment Gateways
  STRIPE: {
    PUBLIC_KEY: process.env.STRIPE_PUBLIC_KEY || '',
    SECRET_KEY: process.env.STRIPE_SECRET_KEY || '',
    WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET || '',
  },
  
  PAYPAL: {
    CLIENT_ID: process.env.PAYPAL_CLIENT_ID || '',
    CLIENT_SECRET: process.env.PAYPAL_CLIENT_SECRET || '',
  },
  
  // Sports Data APIs
  SPORTS_API_KEY: process.env.SPORTS_API_KEY || '',
  ODDS_API_KEY: process.env.ODDS_API_KEY || '',
  LIVE_SCORES_API_KEY: process.env.LIVE_SCORES_API_KEY || '',
  
  // Authentication
  NEXTAUTH_URL: process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL,
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || '',
  
  // Google OAuth
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID || '',
    CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET || '',
  },
  
  // Email
  SMTP: {
    HOST: process.env.SMTP_HOST || '',
    PORT: parseInt(process.env.SMTP_PORT || '587'),
    USER: process.env.SMTP_USER || '',
    PASS: process.env.SMTP_PASS || '',
  },
  
  // File Storage
  AWS: {
    ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID || '',
    SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY || '',
    REGION: process.env.AWS_REGION || 'us-east-1',
    S3_BUCKET: process.env.AWS_S3_BUCKET || '',
  },
  
  // CDN
  CDN_URL: process.env.NEXT_PUBLIC_CDN_URL || '',
  
  // Analytics
  GOOGLE_ANALYTICS_ID: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID || '',
  HOTJAR_ID: process.env.NEXT_PUBLIC_HOTJAR_ID || '',
  
  // Security
  RATE_LIMIT: {
    MAX: parseInt(process.env.RATE_LIMIT_MAX || '100'),
    WINDOW: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'),
  },
  SESSION_TIMEOUT: parseInt(process.env.SESSION_TIMEOUT || '3600000'),
  
  // Monitoring
  SENTRY_DSN: process.env.SENTRY_DSN || '',
  DATADOG_API_KEY: process.env.DATADOG_API_KEY || '',
  
  // Feature Flags
  FEATURES: {
    LIVE_BETTING: process.env.NEXT_PUBLIC_ENABLE_LIVE_BETTING === 'true',
    CRYPTO_PAYMENTS: process.env.NEXT_PUBLIC_ENABLE_CRYPTO_PAYMENTS === 'true',
    CASINO: process.env.NEXT_PUBLIC_ENABLE_CASINO === 'true',
    SPORTS_BETTING: process.env.NEXT_PUBLIC_ENABLE_SPORTS_BETTING === 'true',
  },
  
  // Regulatory
  REGULATORY: {
    LICENSE_NUMBER: process.env.NEXT_PUBLIC_LICENSE_NUMBER || '',
    JURISDICTION: process.env.NEXT_PUBLIC_JURISDICTION || '',
    MIN_AGE: parseInt(process.env.NEXT_PUBLIC_MIN_AGE || '18'),
  },
  
  // Development
  DEBUG: process.env.NEXT_PUBLIC_ENABLE_DEBUG === 'true',
  MOCK_PAYMENTS: process.env.NEXT_PUBLIC_MOCK_PAYMENTS === 'true',
  MOCK_SPORTS_DATA: process.env.NEXT_PUBLIC_MOCK_SPORTS_DATA === 'true',
} as const

// Type-safe environment access
export type Environment = typeof env

// Helper functions
export const isDevelopment = env.NODE_ENV === 'development'
export const isProduction = env.NODE_ENV === 'production'
export const isTest = env.NODE_ENV === 'test'

// Feature flag helpers
export const isFeatureEnabled = (feature: keyof typeof env.FEATURES): boolean => {
  return env.FEATURES[feature]
}

// Validation helpers
export const validateApiKey = (key: string, name: string): void => {
  if (isProduction && !key) {
    throw new Error(`${name} API key is required in production`)
  }
}

// Export for use in other files
export default env
