// Core betting platform types

export interface User {
  id: string
  email: string
  username: string
  firstName?: string
  lastName?: string
  avatar?: string
  balance: number
  currency: string
  isVerified: boolean
  createdAt: string
}

export interface Sport {
  id: string
  name: string
  slug: string
  icon: string
  isActive: boolean
  eventCount: number
}

export interface League {
  id: string
  sportId: string
  name: string
  country: string
  flag?: string
  isActive: boolean
}

export interface Team {
  id: string
  name: string
  logo?: string
  country?: string
}

export interface Event {
  id: string
  leagueId: string
  homeTeam: Team
  awayTeam: Team
  startTime: string
  status: 'scheduled' | 'live' | 'finished' | 'cancelled'
  isLive: boolean
  homeScore?: number
  awayScore?: number
  minute?: number
  period?: string
  markets: Market[]
}

export interface Market {
  id: string
  eventId: string
  name: string
  type: MarketType
  isActive: boolean
  isSuspended: boolean
  outcomes: Outcome[]
}

export type MarketType = 
  | 'match_winner'
  | 'over_under'
  | 'handicap'
  | 'both_teams_score'
  | 'correct_score'
  | 'first_goal_scorer'

export interface Outcome {
  id: string
  marketId: string
  name: string
  odds: number
  isActive: boolean
  trend?: 'up' | 'down' | 'neutral'
}

export interface BetSelection {
  outcomeId: string
  eventId: string
  marketId: string
  eventName: string
  marketName: string
  outcomeName: string
  odds: number
  isLive: boolean
}

export interface Bet {
  id: string
  userId: string
  type: 'single' | 'combo' | 'system'
  selections: BetSelection[]
  stake: number
  potentialWin: number
  totalOdds: number
  status: 'pending' | 'won' | 'lost' | 'void' | 'cancelled'
  placedAt: string
  settledAt?: string
}

export interface CasinoGame {
  id: string
  name: string
  provider: string
  category: string
  thumbnail: string
  isLive: boolean
  minBet: number
  maxBet: number
  rtp: number
  popularity: number
  isNew: boolean
  isFeatured: boolean
}

export interface Transaction {
  id: string
  userId: string
  type: 'deposit' | 'withdrawal' | 'bet_stake' | 'bet_win' | 'bonus'
  amount: number
  currency: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  method?: string
  createdAt: string
  completedAt?: string
}

export interface Notification {
  id: string
  userId: string
  type: 'bet_settled' | 'deposit_completed' | 'withdrawal_processed' | 'promotion'
  title: string
  message: string
  isRead: boolean
  createdAt: string
}

export interface LiveOddsUpdate {
  outcomeId: string
  odds: number
  previousOdds: number
  timestamp: number
}

export interface EventUpdate {
  eventId: string
  homeScore?: number
  awayScore?: number
  minute?: number
  period?: string
  status?: string
  timestamp: number
}

// UI State types
export interface BettingSlipState {
  isOpen: boolean
  selections: BetSelection[]
  stake: number
  betType: 'single' | 'combo' | 'system'
  totalOdds: number
  potentialWin: number
}

export interface AppState {
  user: User | null
  isAuthenticated: boolean
  theme: 'light' | 'dark'
  currency: string
  language: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp: number
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form types
export interface LoginForm {
  email: string
  password: string
  rememberMe: boolean
}

export interface RegisterForm {
  email: string
  username: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  dateOfBirth: string
  country: string
  currency: string
  acceptTerms: boolean
  acceptPrivacy: boolean
}

export interface DepositForm {
  amount: number
  method: string
  currency: string
}

export interface WithdrawalForm {
  amount: number
  method: string
  address?: string
  accountNumber?: string
}

// Statistics types
export interface UserStats {
  totalBets: number
  totalWins: number
  totalLosses: number
  winRate: number
  totalStaked: number
  totalWon: number
  netProfit: number
  biggestWin: number
  currentStreak: number
  longestStreak: number
}

export interface PlatformStats {
  totalUsers: number
  totalBets: number
  totalVolume: number
  liveEvents: number
  popularSports: Sport[]
  biggestWins: Bet[]
}
