'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ClockIcon, FireIcon } from '@heroicons/react/24/outline'
import { mockEvents } from '@/data/mock-data'
import { useBetting } from '@/store/betting-store'
import { BetSelection } from '@/types'

export function SportsGrid() {
  const [filter, setFilter] = useState<'all' | 'live' | 'upcoming'>('all')
  const [mounted, setMounted] = useState(false)
  const { addSelection } = useBetting()

  useEffect(() => {
    setMounted(true)
  }, [])

  const filteredEvents = mockEvents.filter(event => {
    if (filter === 'live') return event.isLive
    if (filter === 'upcoming') return !event.isLive
    return true
  })

  const handleOddsClick = (event: any, market: any, outcome: any) => {
    const selection: BetSelection = {
      outcomeId: outcome.id,
      eventId: event.id,
      marketId: market.id,
      eventName: `${event.homeTeam.name} vs ${event.awayTeam.name}`,
      marketName: market.name,
      outcomeName: outcome.name,
      odds: outcome.odds,
      isLive: event.isLive,
    }
    addSelection(selection)
  }

  const formatTime = (dateString: string) => {
    if (!mounted) return '--:--' // Prevent hydration mismatch
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  return (
    <div className="space-y-6">
      {/* Filter Tabs */}
      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg max-w-fit">
        {[
          { id: 'all', label: 'All Events' },
          { id: 'live', label: 'Live' },
          { id: 'upcoming', label: 'Upcoming' },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setFilter(tab.id as any)}
            className={`py-2 px-4 rounded-md font-medium transition-all duration-200 whitespace-nowrap ${
              filter === tab.id
                ? 'bg-primary-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-dark-700'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Events Grid */}
      <div className="space-y-4">
        {filteredEvents.map((event, index) => (
          <motion.div
            key={event.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="bg-dark-800 rounded-xl border border-dark-700 hover:border-dark-600 transition-colors overflow-hidden"
          >
            {/* Event Header */}
            <div className="p-4 border-b border-dark-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {event.isLive && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-red-400 text-sm font-medium">LIVE</span>
                    </div>
                  )}
                  {!event.isLive && (
                    <div className="flex items-center space-x-2 text-gray-400">
                      <ClockIcon className="h-4 w-4" />
                      <span className="text-sm">{formatTime(event.startTime)}</span>
                    </div>
                  )}
                </div>
                
                {event.isLive && event.minute && (
                  <div className="text-primary-400 font-medium">
                    {event.minute}' {event.period}
                  </div>
                )}
              </div>
            </div>

            {/* Teams and Score */}
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-white">
                        {event.homeTeam.name.charAt(0)}
                      </span>
                    </div>
                    <span className="text-white font-medium">{event.homeTeam.name}</span>
                    {event.isLive && event.homeScore !== undefined && (
                      <span className="text-2xl font-bold text-white">{event.homeScore}</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-white">
                        {event.awayTeam.name.charAt(0)}
                      </span>
                    </div>
                    <span className="text-white font-medium">{event.awayTeam.name}</span>
                    {event.isLive && event.awayScore !== undefined && (
                      <span className="text-2xl font-bold text-white">{event.awayScore}</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Markets */}
              <div className="space-y-4">
                {event.markets.map((market) => (
                  <div key={market.id}>
                    <h4 className="text-gray-400 text-sm font-medium mb-2">
                      {market.name}
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {market.outcomes.map((outcome) => (
                        <motion.button
                          key={outcome.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleOddsClick(event, market, outcome)}
                          className={`p-3 rounded-lg border transition-all duration-200 ${
                            outcome.trend === 'up'
                              ? 'bg-green-500/10 border-green-500/30 hover:bg-green-500/20'
                              : outcome.trend === 'down'
                              ? 'bg-red-500/10 border-red-500/30 hover:bg-red-500/20'
                              : 'bg-dark-700 border-dark-600 hover:bg-dark-600'
                          }`}
                        >
                          <div className="text-white font-medium text-sm mb-1">
                            {outcome.name}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-primary-400 font-bold">
                              {outcome.odds.toFixed(2)}
                            </span>
                            {outcome.trend && outcome.trend !== 'neutral' && (
                              <span className={`text-xs ${
                                outcome.trend === 'up' ? 'text-green-400' : 'text-red-400'
                              }`}>
                                {outcome.trend === 'up' ? '↗' : '↘'}
                              </span>
                            )}
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {filteredEvents.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">No events found</div>
          <div className="text-gray-500 text-sm">
            Try adjusting your filter or check back later
          </div>
        </div>
      )}
    </div>
  )
}
