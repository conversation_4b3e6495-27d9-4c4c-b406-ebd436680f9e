'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface LoadingContextType {
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export const useLoading = () => {
  const context = useContext(LoadingContext)
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

interface LoadingProviderProps {
  children: ReactNode
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [dots, setDots] = useState('')

  useEffect(() => {
    // Set initial loading state and wait for page to be ready
    const handleLoad = () => {
      // Add minimum loading time for smooth UX, but respect actual load time
      const minLoadTime = 1000 // Minimum 1 second for branding
      const startTime = performance.now()

      const finishLoading = () => {
        const elapsed = performance.now() - startTime
        const remainingTime = Math.max(0, minLoadTime - elapsed)

        setTimeout(() => {
          setIsLoading(false)
        }, remainingTime)
      }

      // Wait for all resources to load
      if (document.readyState === 'complete') {
        finishLoading()
      } else {
        window.addEventListener('load', finishLoading)
        return () => window.removeEventListener('load', finishLoading)
      }
    }

    handleLoad()
  }, [])

  useEffect(() => {
    if (isLoading) {
      const interval = setInterval(() => {
        setDots(prev => prev.length >= 3 ? '' : prev + '.')
      }, 500)

      return () => clearInterval(interval)
    }
  }, [isLoading])

  return (
    <LoadingContext.Provider value={{ isLoading, setIsLoading }}>
      <AnimatePresence mode="wait">
        {isLoading && (
          <motion.div
            key="loading"
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="fixed inset-0 z-[9999] bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 flex items-center justify-center"
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,215,0,0.1),transparent_50%)]" />
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(255,165,0,0.1),transparent_50%)]" />
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(255,140,0,0.1),transparent_50%)]" />
            </div>

            {/* Floating Elements */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-primary-500/20 rounded-full"
                  initial={{ 
                    x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1000), 
                    y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 800),
                    scale: 0 
                  }}
                  animate={{
                    y: [0, -100, 0],
                    scale: [0, 1, 0],
                    opacity: [0, 0.6, 0]
                  }}
                  transition={{
                    duration: 3 + Math.random() * 2,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>

            {/* Main Loading Content */}
            <div className="relative z-10 flex flex-col items-center space-y-8">
              {/* Animated Logo */}
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="relative"
              >
                {/* Outer Ring */}
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                  className="w-28 h-28 border-4 border-transparent border-t-primary-500 border-r-secondary-500 rounded-full"
                />
                
                {/* Middle Ring */}
                <motion.div
                  animate={{ rotate: -360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-2 w-20 h-20 border-3 border-transparent border-b-primary-400 border-l-secondary-400 rounded-full"
                />
                
                {/* Inner Ring */}
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-4 w-16 h-16 border-2 border-transparent border-t-yellow-400 border-r-orange-400 rounded-full"
                />
                
                {/* Center Logo */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  <div className="w-14 h-14 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-2xl">
                    <span className="text-white font-bold text-2xl">K</span>
                  </div>
                </motion.div>
              </motion.div>

              {/* Brand Text */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
                className="text-center space-y-3"
              >
                <h1 className="text-3xl font-bold text-white">
                  Kesar <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-secondary-500">Mango</span>
                </h1>
                <p className="text-gray-400 text-base">Premium Sports & Casino Betting</p>
              </motion.div>

              {/* Loading Text */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.4 }}
                className="text-center"
              >
                <p className="text-gray-500 text-sm font-medium">
                  Loading your betting experience{dots}
                </p>
              </motion.div>

              {/* Progress Bar */}
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "100%" }}
                transition={{ delay: 1, duration: 0.8 }}
                className="w-64 h-1.5 bg-dark-700 rounded-full overflow-hidden"
              >
                <motion.div
                  initial={{ x: "-100%" }}
                  animate={{ x: "100%" }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="h-full w-1/3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full shadow-lg"
                />
              </motion.div>
            </div>

            {/* Bottom Branding */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.6 }}
              className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center"
            >
              <p className="text-gray-600 text-xs">
                Powered by cutting-edge technology
              </p>
              <div className="flex items-center justify-center space-x-1 mt-2">
                <div className="w-1.5 h-1.5 bg-primary-500 rounded-full animate-pulse" />
                <div className="w-1.5 h-1.5 bg-secondary-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
                <div className="w-1.5 h-1.5 bg-primary-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoading ? 0 : 1 }}
        transition={{ duration: 0.5, delay: isLoading ? 0 : 0.3 }}
      >
        {children}
      </motion.div>
    </LoadingContext.Provider>
  )
}
