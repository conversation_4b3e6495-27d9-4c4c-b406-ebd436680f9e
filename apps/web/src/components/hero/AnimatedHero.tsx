'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/store/auth-store'
import { UserDashboard } from '@/components/dashboard/UserDashboard'
import { LoginModal } from '@/components/auth/LoginModal'
import { CasinoAnimation } from '@/components/animations/CasinoAnimation'

export function AnimatedHero() {
  const { isAuthenticated, isLoading } = useAuth()
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-transparent border-t-primary-500 rounded-full"
        />
      </div>
    )
  }

  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative min-h-[300px] bg-dark-800/30 rounded-xl border border-dark-700/50 backdrop-blur-sm py-6 lg:py-8 overflow-hidden"
      >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-transparent to-secondary-500/5 rounded-xl" />
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-secondary-500/10 rounded-full blur-3xl" />
      </div>

      {/* Content */}
      <div className="relative z-10 w-full px-6 lg:px-8">
        {isAuthenticated ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="text-left">
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-xl lg:text-2xl text-gray-300 mb-6"
              >
                Experience the ultimate betting platform with live sports betting, casino games, and real-time odds.
                Join thousands of winners today!
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="flex flex-wrap gap-3 mb-6"
              >
                <div className="bg-dark-800/50 backdrop-blur-sm rounded-xl px-4 py-2 border border-dark-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span className="text-gray-300 text-sm font-medium">Live Events</span>
                  </div>
                </div>
                <div className="bg-dark-800/50 backdrop-blur-sm rounded-xl px-4 py-2 border border-dark-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                    <span className="text-gray-300 text-sm font-medium">Instant Withdrawals</span>
                  </div>
                </div>
                <div className="bg-dark-800/50 backdrop-blur-sm rounded-xl px-4 py-2 border border-dark-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
                    <span className="text-gray-300 text-sm font-medium">24/7 Support</span>
                  </div>
                </div>
              </motion.div>

              <UserDashboard />
            </div>

            <div className="hidden lg:block">
              <CasinoAnimation />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="text-left">
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-xl lg:text-2xl text-gray-300 mb-6"
              >
                Experience the ultimate betting platform with live sports betting, casino games, and real-time odds.
                Join thousands of winners today!
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-wrap gap-3 mb-6"
              >
                <div className="bg-dark-800/50 backdrop-blur-sm rounded-xl px-4 py-2 border border-dark-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span className="text-gray-300 text-sm font-medium">Live Events</span>
                  </div>
                </div>
                <div className="bg-dark-800/50 backdrop-blur-sm rounded-xl px-4 py-2 border border-dark-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                    <span className="text-gray-300 text-sm font-medium">Instant Withdrawals</span>
                  </div>
                </div>
                <div className="bg-dark-800/50 backdrop-blur-sm rounded-xl px-4 py-2 border border-dark-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
                    <span className="text-gray-300 text-sm font-medium">24/7 Support</span>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsLoginModalOpen(true)}
                  className="bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 text-white font-bold py-4 px-8 rounded-xl text-lg transition-all duration-200 shadow-lg"
                >
                  Start Betting Now
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    // Scroll to sports section or games section
                    const sportsSection = document.querySelector('[data-section="sports"]')
                    if (sportsSection) {
                      sportsSection.scrollIntoView({ behavior: 'smooth' })
                    }
                  }}
                  className="bg-dark-800 hover:bg-dark-700 text-white font-semibold py-4 px-8 rounded-xl text-lg transition-all duration-200 border border-dark-600"
                >
                  Explore Games
                </motion.button>
              </motion.div>
            </div>

            <div className="hidden lg:block">
              <CasinoAnimation />
            </div>
          </div>
        )}
      </div>
    </motion.div>

    {/* Login Modal */}
    <LoginModal
      isOpen={isLoginModalOpen}
      onClose={() => setIsLoginModalOpen(false)}
    />
  </>
  )
}
