'use client'

import { motion } from 'framer-motion'
import { InformationCircleIcon, ArrowRightIcon } from '@heroicons/react/24/outline'
import { useAuth } from '@/store/auth-store'

export function AuthPromptBanner() {
  const { isAuthenticated } = useAuth()

  if (isAuthenticated) {
    return null
  }

  const scrollToLogin = () => {
    const heroSection = document.querySelector('main')
    heroSection?.scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-r from-primary-600/10 to-secondary-600/10 border border-primary-500/20 rounded-lg p-4 mb-6"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-primary-400 flex-shrink-0" />
          <div>
            <p className="text-white font-medium text-sm">
              Login to unlock personalized features
            </p>
            <p className="text-gray-400 text-xs">
              Track your bets, view history, and access exclusive promotions
            </p>
          </div>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={scrollToLogin}
          className="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
        >
          <span>Login</span>
          <ArrowRightIcon className="h-4 w-4" />
        </motion.button>
      </div>
    </motion.div>
  )
}
