'use client'

import { useEffect, useState, useRef } from 'react'
import { motion, useInView } from 'framer-motion'

interface AnimatedCounterProps {
  end: number
  duration?: number
  prefix?: string
  suffix?: string
  decimals?: number
  className?: string
  delay?: number
  formatNumber?: boolean
}

export function AnimatedCounter({
  end,
  duration = 2,
  prefix = '',
  suffix = '',
  decimals = 0,
  className = '',
  delay = 0,
  formatNumber = true
}: AnimatedCounterProps) {
  const [count, setCount] = useState(0)
  const [hasAnimated, setHasAnimated] = useState(false)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  useEffect(() => {
    if (isInView && !hasAnimated) {
      setHasAnimated(true)
      
      const timer = setTimeout(() => {
        let startTime: number
        let animationFrame: number

        const animate = (timestamp: number) => {
          if (!startTime) startTime = timestamp
          const progress = Math.min((timestamp - startTime) / (duration * 1000), 1)
          
          // Use easing function for smooth animation
          const easeOutQuart = 1 - Math.pow(1 - progress, 4)
          const currentCount = Math.floor(easeOutQuart * end)
          
          setCount(currentCount)

          if (progress < 1) {
            animationFrame = requestAnimationFrame(animate)
          } else {
            setCount(end) // Ensure we end at exact value
          }
        }

        animationFrame = requestAnimationFrame(animate)

        return () => {
          if (animationFrame) {
            cancelAnimationFrame(animationFrame)
          }
        }
      }, delay * 1000)

      return () => clearTimeout(timer)
    }
  }, [isInView, hasAnimated, end, duration, delay])

  const formatValue = (value: number) => {
    if (decimals > 0) {
      return value.toFixed(decimals)
    }
    if (formatNumber && value >= 1000) {
      return value.toLocaleString()
    }
    return value.toString()
  }

  return (
    <motion.span
      ref={ref}
      initial={{ opacity: 0, scale: 0.5 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: delay }}
      className={className}
    >
      {prefix}{formatValue(count)}{suffix}
    </motion.span>
  )
}

// Specialized counter for currency values
export function CurrencyCounter({
  end,
  duration = 2,
  delay = 0,
  className = '',
  currency = '$',
  showDecimals = true
}: {
  end: number
  duration?: number
  delay?: number
  className?: string
  currency?: string
  showDecimals?: boolean
}) {
  return (
    <AnimatedCounter
      end={end}
      duration={duration}
      delay={delay}
      className={className}
      prefix={currency}
      decimals={showDecimals ? 1 : 0}
      formatNumber={true}
    />
  )
}

// Specialized counter for percentage values
export function PercentageCounter({
  end,
  duration = 2,
  delay = 0,
  className = '',
  showSign = true
}: {
  end: number
  duration?: number
  delay?: number
  className?: string
  showSign?: boolean
}) {
  return (
    <AnimatedCounter
      end={end}
      duration={duration}
      delay={delay}
      className={className}
      prefix={showSign && end > 0 ? '+' : ''}
      suffix='%'
      decimals={1}
      formatNumber={false}
    />
  )
}

// Specialized counter for large numbers with M/K suffixes
export function LargeNumberCounter({
  end,
  duration = 2,
  delay = 0,
  className = '',
  autoSuffix = true
}: {
  end: number
  duration?: number
  delay?: number
  className?: string
  autoSuffix?: boolean
}) {
  const getSuffix = (value: number) => {
    if (!autoSuffix) return ''
    if (value >= 1000000) return 'M'
    if (value >= 1000) return 'K'
    return ''
  }

  const getDisplayValue = (value: number) => {
    if (!autoSuffix) return value
    if (value >= 1000000) return value / 1000000
    if (value >= 1000) return value / 1000
    return value
  }

  return (
    <AnimatedCounter
      end={getDisplayValue(end)}
      duration={duration}
      delay={delay}
      className={className}
      suffix={getSuffix(end)}
      decimals={end >= 1000000 ? 1 : 0}
      formatNumber={false}
    />
  )
}
