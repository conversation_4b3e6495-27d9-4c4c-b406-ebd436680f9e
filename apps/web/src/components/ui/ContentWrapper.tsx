'use client'

import { ReactNode } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/store/auth-store'

interface ContentWrapperProps {
  children: ReactNode
  className?: string
}

export function ContentWrapper({ children, className = '' }: ContentWrapperProps) {
  const { isAuthenticated } = useAuth()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className={`relative ${className}`}
    >
      {/* Subtle overlay when not authenticated */}
      {!isAuthenticated && (
        <div className="absolute inset-0 bg-dark-950/20 backdrop-blur-[0.5px] rounded-lg z-10 pointer-events-none" />
      )}
      
      {/* Content */}
      <div className={!isAuthenticated ? 'opacity-90' : 'opacity-100'}>
        {children}
      </div>
    </motion.div>
  )
}
