'use client'

import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'

export function CasinoAnimation() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  if (!isVisible) return null

  return (
    <div className="relative w-full h-full min-h-[200px] lg:min-h-[300px] overflow-hidden">
      {/* Main Casino Elements */}
      <div className="absolute inset-0 flex items-center justify-center">
        
        {/* Floating Casino Chips */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="absolute top-4 right-8"
        >
          <motion.div
            animate={{ 
              rotate: [0, 360],
              y: [0, -10, 0]
            }}
            transition={{ 
              rotate: { duration: 8, repeat: Infinity, ease: "linear" },
              y: { duration: 3, repeat: Infinity, ease: "easeInOut" }
            }}
            className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-700 rounded-full border-4 border-white shadow-lg flex items-center justify-center"
          >
            <span className="text-white font-bold text-xs">100</span>
          </motion.div>
        </motion.div>

        {/* Dice Animation */}
        <motion.div
          initial={{ scale: 0, rotate: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="absolute top-12 left-8"
        >
          <motion.div
            animate={{ 
              rotate: [0, 90, 180, 270, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 4, 
              repeat: Infinity, 
              ease: "easeInOut",
              times: [0, 0.25, 0.5, 0.75, 1]
            }}
            className="w-10 h-10 bg-white rounded-lg shadow-lg border-2 border-gray-300 flex items-center justify-center"
          >
            <div className="grid grid-cols-2 gap-1 p-1">
              <div className="w-1.5 h-1.5 bg-black rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-black rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-black rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-black rounded-full"></div>
            </div>
          </motion.div>
        </motion.div>

        {/* Playing Cards */}
        <motion.div
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 0.6 }}
          className="absolute bottom-8 right-4"
        >
          <div className="relative">
            <motion.div
              animate={{ rotate: [0, 5, -5, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="w-8 h-12 bg-white rounded-lg shadow-lg border border-gray-300 flex items-center justify-center transform rotate-12"
            >
              <span className="text-red-500 font-bold text-lg">♥</span>
            </motion.div>
            <motion.div
              animate={{ rotate: [0, -5, 5, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
              className="absolute -top-2 -left-2 w-8 h-12 bg-white rounded-lg shadow-lg border border-gray-300 flex items-center justify-center transform -rotate-12"
            >
              <span className="text-black font-bold text-lg">♠</span>
            </motion.div>
          </div>
        </motion.div>

        {/* Slot Machine Reels Effect */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="absolute bottom-4 left-4"
        >
          <div className="flex space-x-1">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                animate={{ 
                  y: [0, -20, 0],
                  rotateX: [0, 360, 0]
                }}
                transition={{ 
                  duration: 1.5, 
                  repeat: Infinity, 
                  delay: index * 0.2,
                  ease: "easeInOut"
                }}
                className="w-6 h-8 bg-gradient-to-b from-yellow-400 to-yellow-600 rounded border-2 border-yellow-700 flex items-center justify-center"
              >
                <span className="text-yellow-900 font-bold text-sm">7</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Cricket Ball (for sports betting theme) */}
        <motion.div
          initial={{ x: -100, y: 50 }}
          animate={{ x: 0, y: 0 }}
          transition={{ duration: 1.2, delay: 1 }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        >
          <motion.div
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              rotate: { duration: 3, repeat: Infinity, ease: "linear" },
              scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
            }}
            className="w-16 h-16 bg-gradient-to-br from-red-600 to-red-800 rounded-full shadow-lg relative"
          >
            {/* Cricket ball seam */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-12 h-0.5 bg-white rounded-full"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center rotate-90">
              <div className="w-8 h-0.5 bg-white rounded-full"></div>
            </div>
          </motion.div>
        </motion.div>

        {/* Floating Coins */}
        {[...Array(3)].map((_, index) => (
          <motion.div
            key={index}
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 1, delay: 1.2 + index * 0.2 }}
            className={`absolute ${
              index === 0 ? 'top-8 right-16' : 
              index === 1 ? 'bottom-16 left-16' : 
              'top-16 left-1/3'
            }`}
          >
            <motion.div
              animate={{ 
                rotateY: [0, 180, 360],
                y: [0, -5, 0]
              }}
              transition={{ 
                duration: 2 + index * 0.5, 
                repeat: Infinity, 
                ease: "easeInOut" 
              }}
              className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full shadow-lg border-2 border-yellow-700 flex items-center justify-center"
            >
              <span className="text-yellow-900 font-bold text-xs">$</span>
            </motion.div>
          </motion.div>
        ))}

        {/* Sparkle Effects */}
        {[...Array(5)].map((_, index) => (
          <motion.div
            key={index}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: [0, 1, 0],
              opacity: [0, 1, 0]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              delay: index * 0.4,
              ease: "easeInOut"
            }}
            className={`absolute ${
              index === 0 ? 'top-4 left-4' :
              index === 1 ? 'top-8 right-8' :
              index === 2 ? 'bottom-4 left-8' :
              index === 3 ? 'bottom-8 right-4' :
              'top-1/2 right-8'
            }`}
          >
            <div className="w-2 h-2 bg-yellow-400 rounded-full shadow-lg"></div>
          </motion.div>
        ))}
      </div>

      {/* Background Glow Effect */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 2, delay: 0.5 }}
        className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-pink-500/5 to-yellow-500/5 rounded-xl"
      />
    </div>
  )
}
