'use client'

import { motion } from 'framer-motion'
import { 
  TrophyIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  FireIcon,
  StarIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '@/store/auth-store'
import { AnimatedCounter } from '@/components/ui/AnimatedCounter'

// Mock data for user's active bets and history
const activeBets = [
  {
    id: '1',
    match: 'Manchester United vs Liverpool',
    bet: 'Over 2.5 Goals',
    odds: 1.85,
    stake: 50,
    status: 'live',
    timeLeft: '45 min'
  },
  {
    id: '2',
    match: 'Lakers vs Warriors',
    bet: 'Lakers to Win',
    odds: 2.10,
    stake: 25,
    status: 'pending',
    timeLeft: '2 hours'
  }
]

const recentHistory = [
  {
    id: '1',
    match: 'Chelsea vs Arsenal',
    bet: 'Arsenal to Win',
    odds: 1.65,
    stake: 100,
    payout: 165,
    status: 'won',
    date: '2 hours ago'
  },
  {
    id: '2',
    match: 'Real Madrid vs Barcelona',
    bet: 'Over 3.5 Goals',
    odds: 2.20,
    stake: 75,
    payout: 0,
    status: 'lost',
    date: '1 day ago'
  }
]

const favoriteSports = [
  { name: 'Football', icon: '⚽', events: 156 },
  { name: '<PERSON>', icon: '🏀', events: 89 },
  { name: 'Tennis', icon: '🎾', events: 234 }
]

export function UserDashboard() {
  const { user } = useAuth()

  if (!user) return null

  return (
    <div className="w-full space-y-8">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center lg:text-left"
      >
        <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">
          Welcome back, <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-secondary-500">{user.username}</span>
        </h1>
        <p className="text-gray-400 text-lg">Ready to place your next winning bet?</p>
      </motion.div>

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6"
      >
        <div className="bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700">
          <div className="flex items-center space-x-3 mb-2">
            <CurrencyDollarIcon className="h-6 w-6 text-green-400" />
            <span className="text-gray-400 text-sm">Balance</span>
          </div>
          <p className="text-2xl font-bold text-white">
            $<AnimatedCounter end={user.balance} duration={2} delay={0.2} decimals={2} />
          </p>
        </div>

        <div className="bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700">
          <div className="flex items-center space-x-3 mb-2">
            <TrophyIcon className="h-6 w-6 text-yellow-400" />
            <span className="text-gray-400 text-sm">Total Bets</span>
          </div>
          <p className="text-2xl font-bold text-white">
            <AnimatedCounter end={user.totalBets} duration={2} delay={0.3} />
          </p>
        </div>

        <div className="bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700">
          <div className="flex items-center space-x-3 mb-2">
            <ChartBarIcon className="h-6 w-6 text-blue-400" />
            <span className="text-gray-400 text-sm">Winnings</span>
          </div>
          <p className="text-2xl font-bold text-white">
            $<AnimatedCounter end={user.totalWinnings} duration={2} delay={0.4} decimals={2} />
          </p>
        </div>

        <div className="bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700">
          <div className="flex items-center space-x-3 mb-2">
            <FireIcon className="h-6 w-6 text-red-400" />
            <span className="text-gray-400 text-sm">Active Bets</span>
          </div>
          <p className="text-2xl font-bold text-white">
            <AnimatedCounter end={activeBets.length} duration={2} delay={0.5} />
          </p>
        </div>
      </motion.div>

      {/* Active Bets & Recent History */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        {/* Active Bets */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-dark-800 rounded-xl p-6 border border-dark-700"
        >
          <div className="flex items-center space-x-2 mb-6">
            <ClockIcon className="h-5 w-5 text-primary-400" />
            <h3 className="text-lg font-semibold text-white">Active Bets</h3>
          </div>
          
          <div className="space-y-4">
            {activeBets.map((bet, index) => (
              <motion.div
                key={bet.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="bg-dark-700 rounded-lg p-4 border border-dark-600"
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-white font-medium text-sm">{bet.match}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    bet.status === 'live' 
                      ? 'bg-red-500/20 text-red-400' 
                      : 'bg-yellow-500/20 text-yellow-400'
                  }`}>
                    {bet.status === 'live' ? 'LIVE' : 'PENDING'}
                  </span>
                </div>
                <p className="text-gray-400 text-sm mb-2">{bet.bet}</p>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-300">Stake: ${bet.stake}</span>
                  <span className="text-primary-400">Odds: {bet.odds}</span>
                  <span className="text-gray-400">{bet.timeLeft}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Recent History */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="bg-dark-800 rounded-xl p-6 border border-dark-700"
        >
          <div className="flex items-center space-x-2 mb-6">
            <ChartBarIcon className="h-5 w-5 text-secondary-400" />
            <h3 className="text-lg font-semibold text-white">Recent History</h3>
          </div>
          
          <div className="space-y-4">
            {recentHistory.map((bet, index) => (
              <motion.div
                key={bet.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className="bg-dark-700 rounded-lg p-4 border border-dark-600"
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-white font-medium text-sm">{bet.match}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    bet.status === 'won' 
                      ? 'bg-green-500/20 text-green-400' 
                      : 'bg-red-500/20 text-red-400'
                  }`}>
                    {bet.status.toUpperCase()}
                  </span>
                </div>
                <p className="text-gray-400 text-sm mb-2">{bet.bet}</p>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-300">Stake: ${bet.stake}</span>
                  <span className={bet.status === 'won' ? 'text-green-400' : 'text-red-400'}>
                    {bet.status === 'won' ? `+$${bet.payout - bet.stake}` : `-$${bet.stake}`}
                  </span>
                  <span className="text-gray-400">{bet.date}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Favorite Sports */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-dark-800 rounded-xl p-6 border border-dark-700"
      >
        <div className="flex items-center space-x-2 mb-6">
          <StarIcon className="h-5 w-5 text-yellow-400" />
          <h3 className="text-lg font-semibold text-white">Quick Access - Favorite Sports</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {favoriteSports.map((sport, index) => (
            <motion.button
              key={sport.name}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 + index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-dark-700 hover:bg-dark-600 rounded-lg p-4 border border-dark-600 hover:border-primary-500/50 transition-all duration-300"
            >
              <div className="text-center">
                <div className="text-3xl mb-2">{sport.icon}</div>
                <h4 className="text-white font-medium mb-1">{sport.name}</h4>
                <p className="text-gray-400 text-sm">{sport.events} events</p>
              </div>
            </motion.button>
          ))}
        </div>
      </motion.div>
    </div>
  )
}
