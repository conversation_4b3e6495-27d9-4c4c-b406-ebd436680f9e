'use client'

import { motion } from 'framer-motion'
import {
  UsersIcon,
  CurrencyDollarIcon,
  TrophyIcon,
  FireIcon,
} from '@heroicons/react/24/outline'
import { mockPlatformStats, mockRecentWinners } from '@/data/mock-data'
import { AnimatedCounter, <PERSON><PERSON><PERSON>cy<PERSON>ounter, LargeNumberCounter } from '@/components/ui/AnimatedCounter'

const stats = [
  {
    id: 'users',
    label: 'Active Users',
    value: mockPlatformStats.totalUsers,
    displayValue: mockPlatformStats.totalUsers.toLocaleString(),
    change: '+12.5%',
    changeType: 'positive' as const,
    icon: UsersIcon,
    color: 'from-blue-500 to-blue-600',
    type: 'number' as const,
  },
  {
    id: 'volume',
    label: 'Total Volume',
    value: mockPlatformStats.totalVolume / 1000000,
    displayValue: `$${(mockPlatformStats.totalVolume / 1000000).toFixed(1)}M`,
    change: '+8.2%',
    changeType: 'positive' as const,
    icon: CurrencyDollarIcon,
    color: 'from-green-500 to-green-600',
    type: 'currency' as const,
  },
  {
    id: 'bets',
    label: 'Total Bets',
    value: mockPlatformStats.totalBets / 1000000,
    displayValue: (mockPlatformStats.totalBets / 1000000).toFixed(1) + 'M',
    change: '+15.3%',
    changeType: 'positive' as const,
    icon: TrophyIcon,
    color: 'from-purple-500 to-purple-600',
    type: 'large' as const,
  },
  {
    id: 'live',
    label: 'Live Events',
    value: mockPlatformStats.liveEvents,
    displayValue: mockPlatformStats.liveEvents.toString(),
    change: '+5',
    changeType: 'positive' as const,
    icon: FireIcon,
    color: 'from-red-500 to-red-600',
    type: 'number' as const,
  },
]

export function StatsOverview() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4 gap-4 lg:gap-6 xl:gap-8 mb-8">
      {stats.map((stat, index) => (
        <motion.div
          key={stat.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
          className="bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700 hover:border-dark-600 transition-all duration-300 h-full flex flex-col"
        >
          <div className="flex items-center justify-between mb-4">
            <div className={`p-2.5 lg:p-3 rounded-lg bg-gradient-to-r ${stat.color} flex-shrink-0`}>
              <stat.icon className="h-5 w-5 lg:h-6 lg:w-6 text-white" />
            </div>
            <div className={`text-sm font-medium ${
              stat.changeType === 'positive' ? 'text-green-400' : 'text-red-400'
            }`}>
              {stat.change}
            </div>
          </div>

          <div className="flex-1 flex flex-col justify-end">
            <h3 className="text-xl lg:text-2xl font-bold text-white mb-1 leading-tight">
              {stat.type === 'currency' ? (
                <CurrencyCounter
                  end={stat.value}
                  duration={2.5}
                  delay={index * 0.2 + 0.5}
                  currency="$"
                  showDecimals={true}
                />
              ) : stat.type === 'large' ? (
                <LargeNumberCounter
                  end={stat.value}
                  duration={2.5}
                  delay={index * 0.2 + 0.5}
                  autoSuffix={true}
                />
              ) : (
                <AnimatedCounter
                  end={stat.value}
                  duration={2.5}
                  delay={index * 0.2 + 0.5}
                  formatNumber={true}
                />
              )}
              {stat.type === 'currency' && 'M'}
            </h3>
            <p className="text-gray-400 text-sm leading-tight">
              {stat.label}
            </p>
          </div>
        </motion.div>
      ))}
      
      {/* Recent Winners */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="col-span-2 md:col-span-2 lg:col-span-4 xl:col-span-4 2xl:col-span-4 bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white flex items-center gap-2">
            <TrophyIcon className="h-5 w-5 text-yellow-400" />
            Recent Big Winners
          </h3>
          <span className="text-sm text-gray-400">Live updates</span>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {mockRecentWinners.map((winner, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
              className="bg-dark-700 rounded-lg p-4 border border-dark-600"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium">
                  {winner.username}
                </span>
                <span className="text-green-400 font-bold">
                  $<AnimatedCounter
                    end={winner.amount}
                    duration={2}
                    delay={0.8 + index * 0.15}
                    formatNumber={true}
                  />
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">{winner.game}</span>
                <span className="text-gray-500">{winner.time}</span>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  )
}
