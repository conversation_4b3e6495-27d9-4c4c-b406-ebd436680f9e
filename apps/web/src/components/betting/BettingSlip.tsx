'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon,
  TrashIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline'
import { useBetting } from '@/store/betting-store'
import toast from 'react-hot-toast'

export function BettingSlip() {
  const {
    isOpen,
    selections,
    stake,
    betType,
    totalOdds,
    potentialWin,
    closeSlip,
    removeSelection,
    clearSelections,
    setStake,
    setBetType,
    canPlaceBet,
  } = useBetting()

  const [isPlacing, setIsPlacing] = useState(false)

  const handlePlaceBet = async () => {
    if (!canPlaceBet) return

    setIsPlacing(true)
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      toast.success(`Bet placed successfully! Potential win: $${potentialWin.toFixed(2)}`)
      clearSelections()
      closeSlip()
    } catch (error) {
      toast.error('Failed to place bet. Please try again.')
    } finally {
      setIsPlacing(false)
    }
  }

  const handleStakeChange = (value: string) => {
    const numValue = parseFloat(value) || 0
    setStake(numValue)
  }

  const quickStakeAmounts = [10, 25, 50, 100]

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Mobile Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeSlip}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          />

          {/* Betting Slip */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-full max-w-md bg-dark-900 border-l border-dark-700 z-50 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-dark-700">
              <h2 className="text-lg font-semibold text-white">Betting Slip</h2>
              <div className="flex items-center space-x-2">
                {selections.length > 0 && (
                  <button
                    onClick={clearSelections}
                    className="p-2 rounded-lg hover:bg-dark-700 transition-colors"
                    title="Clear all selections"
                  >
                    <TrashIcon className="h-5 w-5 text-gray-400" />
                  </button>
                )}
                <button
                  onClick={closeSlip}
                  className="p-2 rounded-lg hover:bg-dark-700 transition-colors"
                >
                  <XMarkIcon className="h-5 w-5 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">
              {selections.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                  <div className="w-16 h-16 bg-dark-700 rounded-full flex items-center justify-center mb-4">
                    <CurrencyDollarIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-white font-medium mb-2">Your bet slip is empty</h3>
                  <p className="text-gray-400 text-sm">
                    Click on odds to add selections to your bet slip
                  </p>
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  {/* Bet Type Selector */}
                  {selections.length > 1 && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-300">Bet Type</label>
                      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
                        {[
                          { id: 'single', label: 'Single' },
                          { id: 'combo', label: 'Combo' },
                          { id: 'system', label: 'System' },
                        ].map((type) => (
                          <button
                            key={type.id}
                            onClick={() => setBetType(type.id as any)}
                            className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                              betType === type.id
                                ? 'bg-primary-600 text-white'
                                : 'text-gray-400 hover:text-white'
                            }`}
                          >
                            {type.label}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Selections */}
                  <div className="space-y-3">
                    {selections.map((selection, index) => (
                      <motion.div
                        key={selection.outcomeId}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="bg-dark-800 rounded-lg p-3 border border-dark-600"
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <h4 className="text-white font-medium text-sm">
                              {selection.eventName}
                            </h4>
                            <p className="text-gray-400 text-xs">
                              {selection.marketName}
                            </p>
                          </div>
                          <button
                            onClick={() => removeSelection(selection.outcomeId)}
                            className="p-1 rounded hover:bg-dark-700 transition-colors"
                          >
                            <XMarkIcon className="h-4 w-4 text-gray-400" />
                          </button>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-white font-medium">
                            {selection.outcomeName}
                          </span>
                          <div className="flex items-center space-x-2">
                            {selection.isLive && (
                              <span className="text-red-400 text-xs">LIVE</span>
                            )}
                            <span className="text-primary-400 font-bold">
                              {selection.odds.toFixed(2)}
                            </span>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Stake Input */}
                  <div className="space-y-3">
                    <label className="text-sm font-medium text-gray-300">Stake Amount</label>
                    
                    {/* Quick Stake Buttons */}
                    <div className="grid grid-cols-4 gap-2">
                      {quickStakeAmounts.map((amount) => (
                        <button
                          key={amount}
                          onClick={() => setStake(amount)}
                          className={`py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                            stake === amount
                              ? 'bg-primary-600 text-white'
                              : 'bg-dark-700 text-gray-300 hover:bg-dark-600'
                          }`}
                        >
                          ${amount}
                        </button>
                      ))}
                    </div>

                    {/* Custom Stake Input */}
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                        $
                      </span>
                      <input
                        type="number"
                        value={stake || ''}
                        onChange={(e) => handleStakeChange(e.target.value)}
                        placeholder="Enter stake amount"
                        className="w-full pl-8 pr-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  {/* Bet Summary */}
                  <div className="bg-dark-700 rounded-lg p-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Total Odds:</span>
                      <span className="text-white font-medium">
                        {totalOdds.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Stake:</span>
                      <span className="text-white font-medium">
                        ${stake.toFixed(2)}
                      </span>
                    </div>
                    <div className="border-t border-dark-600 pt-2">
                      <div className="flex justify-between">
                        <span className="text-gray-300 font-medium">Potential Win:</span>
                        <span className="text-green-400 font-bold text-lg">
                          ${potentialWin.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            {selections.length > 0 && (
              <div className="p-4 border-t border-dark-700">
                <motion.button
                  whileHover={{ scale: canPlaceBet ? 1.02 : 1 }}
                  whileTap={{ scale: canPlaceBet ? 0.98 : 1 }}
                  onClick={handlePlaceBet}
                  disabled={!canPlaceBet || isPlacing}
                  className={`w-full py-3 rounded-lg font-semibold transition-all duration-200 ${
                    canPlaceBet && !isPlacing
                      ? 'bg-primary-600 hover:bg-primary-700 text-white'
                      : 'bg-dark-600 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isPlacing ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Placing Bet...</span>
                    </div>
                  ) : (
                    `Place Bet - $${potentialWin.toFixed(2)}`
                  )}
                </motion.button>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
