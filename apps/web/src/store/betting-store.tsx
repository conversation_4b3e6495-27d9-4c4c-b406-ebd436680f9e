'use client'

import { createContext, useContext, useReducer, ReactNode } from 'react'
import { BetSelection, BettingSlipState } from '@/types'

// Betting Store Actions
type BettingAction =
  | { type: 'ADD_SELECTION'; payload: BetSelection }
  | { type: 'REMOVE_SELECTION'; payload: string }
  | { type: 'CLEAR_SELECTIONS' }
  | { type: 'SET_STAKE'; payload: number }
  | { type: 'SET_BET_TYPE'; payload: 'single' | 'combo' | 'system' }
  | { type: 'TOGGLE_SLIP' }
  | { type: 'OPEN_SLIP' }
  | { type: 'CLOSE_SLIP' }
  | { type: 'UPDATE_ODDS'; payload: { outcomeId: string; odds: number } }

// Initial state
const initialState: BettingSlipState = {
  isOpen: false,
  selections: [],
  stake: 10,
  betType: 'single',
  totalOdds: 1,
  potentialWin: 0,
}

// Utility functions
const calculateTotalOdds = (selections: BetSelection[], betType: string): number => {
  if (selections.length === 0) return 1
  
  if (betType === 'single') {
    return selections[0]?.odds || 1
  }
  
  if (betType === 'combo') {
    return selections.reduce((total, selection) => total * selection.odds, 1)
  }
  
  // System bets would have more complex calculations
  return selections.reduce((total, selection) => total * selection.odds, 1)
}

const calculatePotentialWin = (stake: number, totalOdds: number): number => {
  return stake * totalOdds
}

// Reducer
function bettingReducer(state: BettingSlipState, action: BettingAction): BettingSlipState {
  switch (action.type) {
    case 'ADD_SELECTION': {
      // Check if selection already exists
      const existingIndex = state.selections.findIndex(
        s => s.outcomeId === action.payload.outcomeId
      )
      
      let newSelections: BetSelection[]
      
      if (existingIndex >= 0) {
        // Update existing selection
        newSelections = state.selections.map((selection, index) =>
          index === existingIndex ? action.payload : selection
        )
      } else {
        // Add new selection
        newSelections = [...state.selections, action.payload]
      }
      
      const totalOdds = calculateTotalOdds(newSelections, state.betType)
      const potentialWin = calculatePotentialWin(state.stake, totalOdds)
      
      return {
        ...state,
        selections: newSelections,
        totalOdds,
        potentialWin,
        isOpen: true,
      }
    }
    
    case 'REMOVE_SELECTION': {
      const newSelections = state.selections.filter(
        s => s.outcomeId !== action.payload
      )
      
      const totalOdds = calculateTotalOdds(newSelections, state.betType)
      const potentialWin = calculatePotentialWin(state.stake, totalOdds)
      
      return {
        ...state,
        selections: newSelections,
        totalOdds,
        potentialWin,
        isOpen: newSelections.length > 0 ? state.isOpen : false,
      }
    }
    
    case 'CLEAR_SELECTIONS':
      return {
        ...state,
        selections: [],
        totalOdds: 1,
        potentialWin: 0,
      }
    
    case 'SET_STAKE': {
      const potentialWin = calculatePotentialWin(action.payload, state.totalOdds)
      return {
        ...state,
        stake: action.payload,
        potentialWin,
      }
    }
    
    case 'SET_BET_TYPE': {
      const totalOdds = calculateTotalOdds(state.selections, action.payload)
      const potentialWin = calculatePotentialWin(state.stake, totalOdds)
      
      return {
        ...state,
        betType: action.payload,
        totalOdds,
        potentialWin,
      }
    }
    
    case 'TOGGLE_SLIP':
      return {
        ...state,
        isOpen: !state.isOpen,
      }
    
    case 'OPEN_SLIP':
      return {
        ...state,
        isOpen: true,
      }
    
    case 'CLOSE_SLIP':
      return {
        ...state,
        isOpen: false,
      }
    
    case 'UPDATE_ODDS': {
      const newSelections = state.selections.map(selection =>
        selection.outcomeId === action.payload.outcomeId
          ? { ...selection, odds: action.payload.odds }
          : selection
      )
      
      const totalOdds = calculateTotalOdds(newSelections, state.betType)
      const potentialWin = calculatePotentialWin(state.stake, totalOdds)
      
      return {
        ...state,
        selections: newSelections,
        totalOdds,
        potentialWin,
      }
    }
    
    default:
      return state
  }
}

// Context
const BettingContext = createContext<{
  state: BettingSlipState
  dispatch: React.Dispatch<BettingAction>
} | null>(null)

// Provider
export function BettingProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(bettingReducer, initialState)
  
  return (
    <BettingContext.Provider value={{ state, dispatch }}>
      {children}
    </BettingContext.Provider>
  )
}

// Hook
export function useBetting() {
  const context = useContext(BettingContext)
  
  if (!context) {
    throw new Error('useBetting must be used within a BettingProvider')
  }
  
  const { state, dispatch } = context
  
  return {
    // State
    ...state,
    
    // Actions
    addSelection: (selection: BetSelection) =>
      dispatch({ type: 'ADD_SELECTION', payload: selection }),
    
    removeSelection: (outcomeId: string) =>
      dispatch({ type: 'REMOVE_SELECTION', payload: outcomeId }),
    
    clearSelections: () =>
      dispatch({ type: 'CLEAR_SELECTIONS' }),
    
    setStake: (stake: number) =>
      dispatch({ type: 'SET_STAKE', payload: stake }),
    
    setBetType: (betType: 'single' | 'combo' | 'system') =>
      dispatch({ type: 'SET_BET_TYPE', payload: betType }),
    
    toggleSlip: () =>
      dispatch({ type: 'TOGGLE_SLIP' }),
    
    openSlip: () =>
      dispatch({ type: 'OPEN_SLIP' }),
    
    closeSlip: () =>
      dispatch({ type: 'CLOSE_SLIP' }),
    
    updateOdds: (outcomeId: string, odds: number) =>
      dispatch({ type: 'UPDATE_ODDS', payload: { outcomeId, odds } }),
    
    // Computed values
    hasSelections: state.selections.length > 0,
    selectionCount: state.selections.length,
    canPlaceBet: state.selections.length > 0 && state.stake > 0,
  }
}
