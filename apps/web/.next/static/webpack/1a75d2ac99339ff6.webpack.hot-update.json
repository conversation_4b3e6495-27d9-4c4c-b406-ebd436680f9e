{"c": ["app/page", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fapps%2Fweb%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmantrarajgotecha%2Fkesar_mango%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=false!"]}