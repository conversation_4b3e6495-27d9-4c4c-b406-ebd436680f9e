"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/WalletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _store_betting_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/betting-store */ \"(app-pages-browser)/./src/store/betting-store.tsx\");\n/* harmony import */ var _store_auth_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth-store */ \"(app-pages-browser)/./src/store/auth-store.tsx\");\n/* harmony import */ var _components_auth_LoginModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/LoginModal */ \"(app-pages-browser)/./src/components/auth/LoginModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Header(param) {\n    let { onMenuClick } = param;\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoginModalOpen, setIsLoginModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { selectionCount, toggleSlip } = (0,_store_betting_store__WEBPACK_IMPORTED_MODULE_2__.useBetting)();\n    const { user, isAuthenticated, logout } = (0,_store_auth_store__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-50 bg-dark-900/95 backdrop-blur-md border-b border-dark-700 w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full px-4 lg:px-6 xl:px-8 2xl:px-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"K\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:block text-xl font-bold text-white\",\n                                                children: \"Kesar Mango\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex flex-1 max-w-sm lg:max-w-md xl:max-w-lg 2xl:max-w-xl mx-4 lg:mx-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search events, teams, or games...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: toggleSlip,\n                                            className: \"relative bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Bet Slip\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"Slip\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 15\n                                                }, this),\n                                                selectionCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                    initial: {\n                                                        scale: 0\n                                                    },\n                                                    animate: {\n                                                        scale: 1\n                                                    },\n                                                    className: \"absolute -top-2 -right-2 bg-secondary-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold\",\n                                                    children: selectionCount\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"relative p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-6 w-6 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: isAuthenticated && user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden md:flex items-center space-x-2 bg-dark-800 px-3 py-2 rounded-lg border border-dark-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 font-medium text-sm\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    user.balance.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.05\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                                        children: [\n                                                            user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: user.avatar,\n                                                                alt: user.username,\n                                                                className: \"h-8 w-8 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-8 w-8 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden lg:block text-white font-medium\",\n                                                                children: user.username\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.05\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        onClick: logout,\n                                                        className: \"p-2 rounded-lg hover:bg-red-600/20 transition-colors group\",\n                                                        title: \"Sign Out\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 group-hover:text-red-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                onClick: ()=>setIsLoginModalOpen(true),\n                                                className: \"flex items-center space-x-2 bg-gradient-to-r from-primary-600 to-secondary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-700 hover:to-secondary-700 transition-all\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Sign In\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden px-4 pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search events, teams, or games...\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginModal__WEBPACK_IMPORTED_MODULE_4__.LoginModal, {\n                isOpen: isLoginModalOpen,\n                onClose: ()=>setIsLoginModalOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                lineNumber: 170,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"m0N6jhV9zfIjVDmm7hrxXqqE5d8=\", false, function() {\n    return [\n        _store_betting_store__WEBPACK_IMPORTED_MODULE_2__.useBetting,\n        _store_auth_store__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Header.tsx\n"));

/***/ })

});