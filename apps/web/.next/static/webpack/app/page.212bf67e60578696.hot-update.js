"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/WalletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,MagnifyingGlassIcon,UserCircleIcon,WalletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _store_betting_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/betting-store */ \"(app-pages-browser)/./src/store/betting-store.tsx\");\n/* harmony import */ var _store_auth_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth-store */ \"(app-pages-browser)/./src/store/auth-store.tsx\");\n/* harmony import */ var _components_auth_LoginModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/LoginModal */ \"(app-pages-browser)/./src/components/auth/LoginModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Header(param) {\n    let { onMenuClick } = param;\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoginModalOpen, setIsLoginModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { selectionCount, toggleSlip } = (0,_store_betting_store__WEBPACK_IMPORTED_MODULE_2__.useBetting)();\n    const { user, isAuthenticated, logout } = (0,_store_auth_store__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-50 bg-dark-900/95 backdrop-blur-md border-b border-dark-700 w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full px-4 lg:px-6 xl:px-8 2xl:px-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-bold text-lg\",\n                                                    children: \"K\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:block text-xl font-bold text-white\",\n                                                children: \"Kesar Mango\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex flex-1 max-w-sm lg:max-w-md xl:max-w-lg 2xl:max-w-xl mx-4 lg:mx-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search events, teams, or games...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        isAuthenticated && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            className: \"hidden sm:flex items-center space-x-2 bg-dark-800 px-4 py-2 rounded-lg border border-dark-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Balance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: [\n                                                                \"$\",\n                                                                user.balance.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: toggleSlip,\n                                            className: \"relative bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Bet Slip\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"Slip\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 15\n                                                }, this),\n                                                selectionCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                    initial: {\n                                                        scale: 0\n                                                    },\n                                                    animate: {\n                                                        scale: 1\n                                                    },\n                                                    className: \"absolute -top-2 -right-2 bg-secondary-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold\",\n                                                    children: selectionCount\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"relative p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-6 w-6 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: isAuthenticated && user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden md:flex items-center space-x-2 bg-dark-800 px-3 py-2 rounded-lg border border-dark-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 font-medium text-sm\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    user.balance.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.05\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-dark-700 transition-colors\",\n                                                        children: [\n                                                            user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: user.avatar,\n                                                                alt: user.username,\n                                                                className: \"h-8 w-8 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-8 w-8 text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden lg:block text-white font-medium\",\n                                                                children: user.username\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.05\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        onClick: logout,\n                                                        className: \"p-2 rounded-lg hover:bg-red-600/20 transition-colors group\",\n                                                        title: \"Sign Out\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 group-hover:text-red-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                onClick: ()=>setIsLoginModalOpen(true),\n                                                className: \"flex items-center space-x-2 bg-gradient-to-r from-primary-600 to-secondary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-700 hover:to-secondary-700 transition-all\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Sign In\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden px-4 pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_MagnifyingGlassIcon_UserCircleIcon_WalletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search events, teams, or games...\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginModal__WEBPACK_IMPORTED_MODULE_4__.LoginModal, {\n                isOpen: isLoginModalOpen,\n                onClose: ()=>setIsLoginModalOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx\",\n                lineNumber: 185,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"m0N6jhV9zfIjVDmm7hrxXqqE5d8=\", false, function() {\n    return [\n        _store_betting_store__WEBPACK_IMPORTED_MODULE_2__.useBetting,\n        _store_auth_store__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Header.tsx\n"));

/***/ })

});