# Technical Implementation Guide

## Quick Start Implementation

### 1. Initial Project Setup

```bash
# Initialize the project structure
mkdir -p apps/{web,api,admin} packages/{shared,database,types} infrastructure/{docker,k8s} docs

# Initialize package.json for monorepo
npm init -y
npm install -g pnpm
pnpm init

# Set up workspace configuration
echo 'packages:
  - "apps/*"
  - "packages/*"' > pnpm-workspace.yaml
```

### 2. Technology Stack Implementation

#### Frontend (Next.js + TypeScript)
```bash
cd apps/web
npx create-next-app@latest . --typescript --tailwind --eslint --app
pnpm add zustand socket.io-client @headlessui/react @heroicons/react
pnpm add -D @types/node
```

#### Backend (Express.js + TypeScript)
```bash
cd apps/api
pnpm init
pnpm add express cors helmet morgan compression
pnpm add socket.io redis ioredis
pnpm add prisma @prisma/client
pnpm add jsonwebtoken bcryptjs
pnpm add -D typescript @types/express @types/node ts-node nodemon
```

#### Database Setup (PostgreSQL + Prisma)
```bash
cd packages/database
pnpm init
pnpm add prisma @prisma/client
npx prisma init
```

### 3. Core Database Schema

```prisma
// packages/database/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  username          String    @unique
  passwordHash      String
  firstName         String?
  lastName          String?
  dateOfBirth       DateTime?
  phoneNumber       String?
  country           String?
  currency          String    @default("USD")
  isVerified        Boolean   @default(false)
  isActive          Boolean   @default(true)
  role              UserRole  @default(PLAYER)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  // Relations
  wallet            Wallet?
  bets              Bet[]
  transactions      Transaction[]
  sessions          UserSession[]
  verifications     Verification[]
  
  @@map("users")
}

model Wallet {
  id            String    @id @default(cuid())
  userId        String    @unique
  balance       Decimal   @default(0) @db.Decimal(15,2)
  bonusBalance  Decimal   @default(0) @db.Decimal(15,2)
  currency      String    @default("USD")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  user          User      @relation(fields: [userId], references: [id])
  
  @@map("wallets")
}

model Sport {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  
  leagues     League[]
  
  @@map("sports")
}

model League {
  id          String    @id @default(cuid())
  sportId     String
  name        String
  slug        String
  country     String?
  isActive    Boolean   @default(true)
  
  sport       Sport     @relation(fields: [sportId], references: [id])
  events      Event[]
  
  @@unique([sportId, slug])
  @@map("leagues")
}

model Event {
  id            String      @id @default(cuid())
  leagueId      String
  homeTeam      String
  awayTeam      String
  startTime     DateTime
  status        EventStatus @default(SCHEDULED)
  homeScore     Int?
  awayScore     Int?
  isLive        Boolean     @default(false)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  
  league        League      @relation(fields: [leagueId], references: [id])
  markets       Market[]
  
  @@map("events")
}

model Market {
  id          String      @id @default(cuid())
  eventId     String
  name        String
  type        MarketType
  isActive    Boolean     @default(true)
  
  event       Event       @relation(fields: [eventId], references: [id])
  outcomes    Outcome[]
  
  @@map("markets")
}

model Outcome {
  id          String    @id @default(cuid())
  marketId    String
  name        String
  odds        Decimal   @db.Decimal(8,2)
  isActive    Boolean   @default(true)
  
  market      Market    @relation(fields: [marketId], references: [id])
  betLegs     BetLeg[]
  
  @@map("outcomes")
}

model Bet {
  id            String      @id @default(cuid())
  userId        String
  type          BetType
  stake         Decimal     @db.Decimal(15,2)
  potentialWin  Decimal     @db.Decimal(15,2)
  totalOdds     Decimal     @db.Decimal(8,2)
  status        BetStatus   @default(PENDING)
  placedAt      DateTime    @default(now())
  settledAt     DateTime?
  
  user          User        @relation(fields: [userId], references: [id])
  legs          BetLeg[]
  
  @@map("bets")
}

model BetLeg {
  id          String    @id @default(cuid())
  betId       String
  outcomeId   String
  odds        Decimal   @db.Decimal(8,2)
  status      BetStatus @default(PENDING)
  
  bet         Bet       @relation(fields: [betId], references: [id])
  outcome     Outcome   @relation(fields: [outcomeId], references: [id])
  
  @@map("bet_legs")
}

model Transaction {
  id            String            @id @default(cuid())
  userId        String
  type          TransactionType
  amount        Decimal           @db.Decimal(15,2)
  currency      String
  status        TransactionStatus @default(PENDING)
  reference     String?           @unique
  paymentMethod String?
  createdAt     DateTime          @default(now())
  completedAt   DateTime?
  
  user          User              @relation(fields: [userId], references: [id])
  
  @@map("transactions")
}

model UserSession {
  id          String    @id @default(cuid())
  userId      String
  token       String    @unique
  ipAddress   String?
  userAgent   String?
  expiresAt   DateTime
  createdAt   DateTime  @default(now())
  
  user        User      @relation(fields: [userId], references: [id])
  
  @@map("user_sessions")
}

model Verification {
  id          String            @id @default(cuid())
  userId      String
  type        VerificationType
  status      VerificationStatus @default(PENDING)
  documentUrl String?
  notes       String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  
  user        User              @relation(fields: [userId], references: [id])
  
  @@map("verifications")
}

// Enums
enum UserRole {
  PLAYER
  VIP
  ADMIN
  OPERATOR
}

enum EventStatus {
  SCHEDULED
  LIVE
  FINISHED
  CANCELLED
  POSTPONED
}

enum MarketType {
  MATCH_WINNER
  OVER_UNDER
  HANDICAP
  BOTH_TEAMS_SCORE
  CORRECT_SCORE
}

enum BetType {
  SINGLE
  COMBO
  SYSTEM
}

enum BetStatus {
  PENDING
  WON
  LOST
  VOID
  CANCELLED
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  BET_STAKE
  BET_WIN
  BONUS
  REFUND
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum VerificationType {
  IDENTITY
  ADDRESS
  PAYMENT_METHOD
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
}
```

### 4. Core API Structure

#### Authentication Middleware
```typescript
// apps/api/src/middleware/auth.ts
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { prisma } from '@packages/database';

interface AuthRequest extends Request {
  user?: any;
}

export const authenticateToken = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: { wallet: true }
    });

    if (!user || !user.isActive) {
      return res.status(403).json({ error: 'User not found or inactive' });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid token' });
  }
};
```

#### Betting API Routes
```typescript
// apps/api/src/routes/betting.ts
import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { placeBet, getBets, calculateOdds } from '../services/betting';

const router = express.Router();

router.use(authenticateToken);

router.post('/place', async (req, res) => {
  try {
    const { outcomes, stake, type } = req.body;
    const bet = await placeBet(req.user.id, outcomes, stake, type);
    res.json(bet);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

router.get('/history', async (req, res) => {
  try {
    const bets = await getBets(req.user.id, req.query);
    res.json(bets);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.post('/calculate-odds', async (req, res) => {
  try {
    const { outcomes, type } = req.body;
    const odds = await calculateOdds(outcomes, type);
    res.json(odds);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

export default router;
```

### 5. Frontend Components

#### Betting Slip Component
```typescript
// apps/web/src/components/BettingSlip.tsx
'use client';

import { useState } from 'react';
import { useBettingStore } from '../stores/betting';

interface BettingSlipProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function BettingSlip({ isOpen, onClose }: BettingSlipProps) {
  const { selections, stake, setStake, placeBet, totalOdds, potentialWin } = useBettingStore();
  const [isPlacing, setIsPlacing] = useState(false);

  const handlePlaceBet = async () => {
    setIsPlacing(true);
    try {
      await placeBet();
      onClose();
    } catch (error) {
      console.error('Failed to place bet:', error);
    } finally {
      setIsPlacing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50">
      <div className="absolute right-0 top-0 h-full w-96 bg-white shadow-lg">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">Betting Slip</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              ×
            </button>
          </div>
        </div>
        
        <div className="p-4 space-y-4">
          {selections.map((selection) => (
            <div key={selection.id} className="border rounded p-3">
              <div className="font-medium">{selection.eventName}</div>
              <div className="text-sm text-gray-600">{selection.marketName}</div>
              <div className="flex justify-between items-center mt-2">
                <span>{selection.outcomeName}</span>
                <span className="font-bold">{selection.odds}</span>
              </div>
            </div>
          ))}
          
          <div className="border-t pt-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Total Odds:</span>
                <span className="font-bold">{totalOdds.toFixed(2)}</span>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Stake</label>
                <input
                  type="number"
                  value={stake}
                  onChange={(e) => setStake(Number(e.target.value))}
                  className="w-full border rounded px-3 py-2"
                  placeholder="Enter stake"
                />
              </div>
              
              <div className="flex justify-between">
                <span>Potential Win:</span>
                <span className="font-bold text-green-600">
                  ${potentialWin.toFixed(2)}
                </span>
              </div>
            </div>
            
            <button
              onClick={handlePlaceBet}
              disabled={isPlacing || selections.length === 0 || stake <= 0}
              className="w-full mt-4 bg-blue-600 text-white py-2 rounded font-medium disabled:opacity-50"
            >
              {isPlacing ? 'Placing Bet...' : 'Place Bet'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### 6. Real-time Updates with Socket.io

#### Server Setup
```typescript
// apps/api/src/socket/index.ts
import { Server } from 'socket.io';
import { authenticateSocket } from './middleware';

export function setupSocket(io: Server) {
  io.use(authenticateSocket);
  
  io.on('connection', (socket) => {
    console.log('User connected:', socket.user.id);
    
    // Join user-specific room
    socket.join(`user:${socket.user.id}`);
    
    // Subscribe to live events
    socket.on('subscribe:events', (eventIds: string[]) => {
      eventIds.forEach(eventId => {
        socket.join(`event:${eventId}`);
      });
    });
    
    // Handle bet placement
    socket.on('place:bet', async (betData) => {
      try {
        const bet = await placeBet(socket.user.id, betData);
        socket.emit('bet:placed', bet);
      } catch (error) {
        socket.emit('bet:error', { error: error.message });
      }
    });
    
    socket.on('disconnect', () => {
      console.log('User disconnected:', socket.user.id);
    });
  });
}
```

### 7. Payment Integration

#### Stripe Integration
```typescript
// apps/api/src/services/payments/stripe.ts
import Stripe from 'stripe';
import { prisma } from '@packages/database';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function createDepositIntent(userId: string, amount: number, currency: string) {
  const paymentIntent = await stripe.paymentIntents.create({
    amount: amount * 100, // Convert to cents
    currency: currency.toLowerCase(),
    metadata: {
      userId,
      type: 'deposit'
    }
  });

  // Create transaction record
  const transaction = await prisma.transaction.create({
    data: {
      userId,
      type: 'DEPOSIT',
      amount,
      currency,
      reference: paymentIntent.id,
      paymentMethod: 'stripe'
    }
  });

  return {
    clientSecret: paymentIntent.client_secret,
    transactionId: transaction.id
  };
}

export async function handleWebhook(event: Stripe.Event) {
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object as Stripe.PaymentIntent;
      await processSuccessfulDeposit(paymentIntent);
      break;
    
    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object as Stripe.PaymentIntent;
      await processFailedDeposit(failedPayment);
      break;
  }
}
```

This technical implementation guide provides the foundation for building your betting platform. Each section can be expanded based on specific requirements and features needed for your platform.
