{"name": "kesar-mango-betting-platform", "version": "1.0.0", "private": true, "description": "Modern sports and casino betting platform", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "start": "turbo run start", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "web:dev": "cd apps/web && npm run dev", "web:build": "cd apps/web && npm run build", "web:start": "cd apps/web && npm run start"}, "devDependencies": {"turbo": "^1.10.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/kesar-mango-betting-platform"}, "keywords": ["betting", "sports", "casino", "gambling", "nextjs", "react", "typescript"], "author": "Kesar Mango Team", "license": "MIT"}