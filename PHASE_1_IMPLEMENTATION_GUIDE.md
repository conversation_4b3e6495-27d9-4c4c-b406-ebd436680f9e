# Phase 1: Project Foundation & Architecture - Implementation Guide

## Overview
This phase establishes the technical foundation for your betting platform. We'll set up the project structure, choose technologies, and design the system architecture.

## Step-by-Step Implementation

### Step 1: Technology Stack Selection & Validation

#### 1.1 Create Technology Validation Project
```bash
# Create validation workspace
mkdir betting-platform-validation
cd betting-platform-validation

# Test Next.js performance with real-time updates
npx create-next-app@latest frontend-test --typescript --tailwind --eslint --app
cd frontend-test

# Install betting-specific dependencies
npm install socket.io-client zustand @headlessui/react chart.js react-chartjs-2
npm install -D @types/node

# Test WebSocket performance
npm install ws @types/ws
```

#### 1.2 Backend Performance Validation
```bash
# Create backend test
mkdir ../backend-test
cd ../backend-test
npm init -y

# Install core dependencies
npm install express socket.io redis ioredis
npm install prisma @prisma/client postgresql
npm install jsonwebtoken bcryptjs helmet cors compression
npm install -D typescript @types/express @types/node ts-node nodemon

# Create performance test
cat > performance-test.js << 'EOF'
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Simulate betting load
let connectedUsers = 0;
let betsPerSecond = 0;

io.on('connection', (socket) => {
  connectedUsers++;
  console.log(`Connected users: ${connectedUsers}`);
  
  socket.on('place_bet', (betData) => {
    betsPerSecond++;
    // Simulate bet processing
    setTimeout(() => {
      socket.emit('bet_confirmed', { 
        id: Math.random().toString(36),
        ...betData 
      });
    }, Math.random() * 100);
  });
  
  socket.on('disconnect', () => {
    connectedUsers--;
  });
});

// Performance monitoring
setInterval(() => {
  console.log(`Bets/sec: ${betsPerSecond}, Connected: ${connectedUsers}`);
  betsPerSecond = 0;
}, 1000);

server.listen(3001, () => {
  console.log('Performance test server running on port 3001');
});
EOF

node performance-test.js
```

#### 1.3 Database Performance Testing
```sql
-- Create test database and run performance tests
CREATE DATABASE betting_performance_test;

-- Test high-volume inserts (simulating bet placement)
CREATE TABLE test_bets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    odds DECIMAL(8,2) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Performance test: Insert 100k bets
DO $$
DECLARE
    i INTEGER := 0;
BEGIN
    WHILE i < 100000 LOOP
        INSERT INTO test_bets (user_id, amount, odds) 
        VALUES (
            (random() * 10000)::INTEGER,
            (random() * 1000)::DECIMAL(10,2),
            (1.5 + random() * 10)::DECIMAL(8,2)
        );
        i := i + 1;
    END LOOP;
END $$;

-- Test query performance
EXPLAIN ANALYZE SELECT * FROM test_bets WHERE user_id = 1234 ORDER BY created_at DESC LIMIT 50;
```

### Step 2: Project Structure Setup

#### 2.1 Initialize Monorepo Structure
```bash
# Return to main project
cd ../../kesar_mango

# Create optimized monorepo structure
mkdir -p {apps/{web,api,admin,mobile},packages/{shared,database,types,ui},infrastructure/{docker,k8s,terraform},docs/{api,deployment,compliance}}

# Initialize workspace
cat > package.json << 'EOF'
{
  "name": "kesar-mango-betting-platform",
  "version": "1.0.0",
  "private": true,
  "workspaces": [
    "apps/*",
    "packages/*"
  ],
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "test": "turbo run test",
    "lint": "turbo run lint",
    "clean": "turbo run clean"
  },
  "devDependencies": {
    "turbo": "^1.10.0",
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0"
  }
}
EOF

# Install turbo for monorepo management
npm install -g pnpm turbo
pnpm install

# Create turbo configuration
cat > turbo.json << 'EOF'
{
  "$schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "dist/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "test": {
      "dependsOn": ["build"],
      "outputs": ["coverage/**"]
    },
    "lint": {
      "outputs": []
    },
    "clean": {
      "cache": false
    }
  }
}
EOF
```

#### 2.2 Setup Frontend Application
```bash
cd apps/web
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir

# Install betting-specific dependencies
pnpm add zustand socket.io-client @headlessui/react @heroicons/react
pnpm add chart.js react-chartjs-2 date-fns
pnpm add @hookform/resolvers react-hook-form zod
pnpm add framer-motion # For smooth animations
pnpm add -D @types/node

# Create optimized Next.js config for betting platform
cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  // Optimize for real-time betting
  compress: true,
  poweredByHeader: false,
  
  // Security headers for betting platform
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' wss: https:;",
          },
        ],
      },
    ];
  },
  
  // Optimize images for betting content
  images: {
    domains: ['images.unsplash.com', 'cdn.betting-platform.com'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Enable SWC minification for better performance
  swcMinify: true,
};

module.exports = nextConfig;
EOF
```

#### 2.3 Setup Backend Application
```bash
cd ../api
pnpm init

# Install core dependencies
pnpm add express cors helmet morgan compression
pnpm add socket.io redis ioredis
pnpm add prisma @prisma/client
pnpm add jsonwebtoken bcryptjs
pnpm add joi express-rate-limit
pnpm add bull bullmq # For job queues
pnpm add winston # For logging

# Development dependencies
pnpm add -D typescript @types/express @types/node @types/jsonwebtoken @types/bcryptjs
pnpm add -D ts-node nodemon concurrently
pnpm add -D jest @types/jest supertest @types/supertest

# Create optimized package.json scripts
cat > package.json << 'EOF'
{
  "name": "@kesar-mango/api",
  "version": "1.0.0",
  "main": "dist/index.js",
  "scripts": {
    "dev": "nodemon src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/**/*.ts",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:studio": "prisma studio"
  },
  "dependencies": {
    // ... dependencies listed above
  },
  "devDependencies": {
    // ... dev dependencies listed above
  }
}
EOF

# Create TypeScript configuration optimized for betting platform
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "commonjs",
    "lib": ["ES2022"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/types/*": ["src/types/*"],
      "@/utils/*": ["src/utils/*"],
      "@/services/*": ["src/services/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}
EOF
```

#### 2.4 Setup Shared Packages
```bash
# Database package
cd ../../packages/database
pnpm init
pnpm add prisma @prisma/client
pnpm add -D typescript

# Initialize Prisma
npx prisma init

# Create optimized schema for betting platform
cat > prisma/schema.prisma << 'EOF'
generator client {
  provider = "prisma-client-js"
  output   = "./generated/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User management optimized for betting
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  username          String    @unique
  passwordHash      String
  firstName         String?
  lastName          String?
  dateOfBirth       DateTime?
  phoneNumber       String?
  country           String?
  currency          String    @default("USD")
  timezone          String    @default("UTC")
  
  // Betting-specific fields
  isVerified        Boolean   @default(false)
  verificationLevel Int       @default(0) // 0=none, 1=email, 2=phone, 3=documents
  isActive          Boolean   @default(true)
  isSuspended       Boolean   @default(false)
  suspensionReason  String?
  
  // Risk management
  riskLevel         RiskLevel @default(LOW)
  maxBetAmount      Decimal?  @db.Decimal(15,2)
  dailyLossLimit    Decimal?  @db.Decimal(15,2)
  
  role              UserRole  @default(PLAYER)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  lastLoginAt       DateTime?
  
  // Relations
  wallet            Wallet?
  bets              Bet[]
  transactions      Transaction[]
  sessions          UserSession[]
  verifications     Verification[]
  gamblingLimits    GamblingLimits?
  
  @@map("users")
  @@index([email])
  @@index([username])
  @@index([isActive, isVerified])
}

// Optimized wallet for high-frequency updates
model Wallet {
  id            String    @id @default(cuid())
  userId        String    @unique
  balance       Decimal   @default(0) @db.Decimal(15,2)
  bonusBalance  Decimal   @default(0) @db.Decimal(15,2)
  lockedBalance Decimal   @default(0) @db.Decimal(15,2) // For pending bets
  currency      String    @default("USD")
  version       Int       @default(1) // For optimistic locking
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  user          User      @relation(fields: [userId], references: [id])
  
  @@map("wallets")
  @@index([userId])
}

// Responsible gambling limits
model GamblingLimits {
  id              String    @id @default(cuid())
  userId          String    @unique
  dailyDepositLimit    Decimal?  @db.Decimal(15,2)
  weeklyDepositLimit   Decimal?  @db.Decimal(15,2)
  monthlyDepositLimit  Decimal?  @db.Decimal(15,2)
  dailyBetLimit        Decimal?  @db.Decimal(15,2)
  weeklyBetLimit       Decimal?  @db.Decimal(15,2)
  monthlyBetLimit      Decimal?  @db.Decimal(15,2)
  sessionTimeLimit     Int?      // minutes
  coolingOffUntil      DateTime?
  selfExcludedUntil    DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  user            User      @relation(fields: [userId], references: [id])
  
  @@map("gambling_limits")
}

// Enums
enum UserRole {
  PLAYER
  VIP
  AFFILIATE
  ADMIN
  OPERATOR
  COMPLIANCE
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
EOF

# Types package
cd ../types
pnpm init
pnpm add -D typescript

cat > src/index.ts << 'EOF'
// Shared types for betting platform
export interface BetRequest {
  selections: BetSelection[];
  stake: number;
  betType: 'single' | 'combo' | 'system';
  currency: string;
}

export interface BetSelection {
  outcomeId: string;
  odds: number;
  eventId: string;
  marketId: string;
}

export interface LiveOdds {
  outcomeId: string;
  odds: number;
  timestamp: number;
  volume: number;
}

export interface RealTimeEvent {
  type: 'odds_update' | 'bet_placed' | 'event_update';
  data: any;
  timestamp: number;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
EOF
```

### Step 3: System Architecture Design

#### 3.1 Create Architecture Documentation
```bash
cd ../../docs
mkdir architecture

cat > architecture/system-overview.md << 'EOF'
# System Architecture Overview

## High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │  Mobile Client  │    │  Admin Panel    │
│   (Next.js)     │    │   (React Native)│    │   (Next.js)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      Load Balancer       │
                    │      (CloudFlare)        │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      API Gateway         │
                    │    (Express.js/Fastify)  │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────▼────────┐    ┌─────────▼─────────┐    ┌─────────▼─────────┐
│  Betting API   │    │   Payment API     │    │   User API        │
│  (Node.js)     │    │   (Node.js)       │    │   (Node.js)       │
└───────┬────────┘    └─────────┬─────────┘    └─────────┬─────────┘
        │                       │                        │
        └───────────────────────┼────────────────────────┘
                               │
                    ┌──────────▼──────────┐
                    │     Message Queue   │
                    │     (Redis/Bull)    │
                    └──────────┬──────────┘
                               │
        ┌──────────────────────┼──────────────────────┐
        │                     │                      │
┌───────▼────────┐  ┌─────────▼─────────┐  ┌─────────▼─────────┐
│   PostgreSQL   │  │      Redis        │  │   External APIs   │
│   (Primary DB) │  │   (Cache/Session) │  │  (Sports Data)    │
└────────────────┘  └───────────────────┘  └───────────────────┘
```

## Microservices Architecture

### Core Services
1. **User Service**: Authentication, profiles, KYC
2. **Betting Service**: Bet placement, odds management
3. **Payment Service**: Deposits, withdrawals, wallets
4. **Event Service**: Sports events, live data
5. **Notification Service**: Real-time updates, emails
6. **Compliance Service**: AML, reporting, limits

### Data Flow
1. User places bet via web/mobile client
2. Request hits load balancer and API gateway
3. Betting service validates bet and checks limits
4. Payment service locks funds in wallet
5. Bet is stored in database with audit trail
6. Real-time update sent via WebSocket
7. Settlement service processes results
EOF

# Create detailed service specifications
cat > architecture/service-specifications.md << 'EOF'
# Service Specifications

## Betting Service API

### Endpoints
- `POST /api/bets` - Place new bet
- `GET /api/bets` - Get user bet history
- `GET /api/bets/:id` - Get specific bet details
- `POST /api/bets/:id/cashout` - Cash out bet early

### Performance Requirements
- Response time: <200ms for bet placement
- Throughput: 10,000 bets/minute
- Availability: 99.9% uptime
- Consistency: ACID compliance for all bet transactions

### Error Handling
- Insufficient funds: HTTP 402
- Invalid odds: HTTP 400
- System maintenance: HTTP 503
- Rate limiting: HTTP 429

## Real-time Communication

### WebSocket Events
- `odds:update` - Live odds changes
- `bet:placed` - Bet confirmation
- `bet:settled` - Bet result
- `event:update` - Live event updates
- `balance:update` - Wallet balance changes

### Connection Management
- Auto-reconnection on disconnect
- Heartbeat every 30 seconds
- Maximum 5 reconnection attempts
- Graceful degradation to polling
EOF
```

#### 3.2 Performance Optimization Strategy
```bash
cat > architecture/performance-strategy.md << 'EOF'
# Performance Optimization Strategy

## Database Optimization

### Indexing Strategy
```sql
-- Critical indexes for betting platform
CREATE INDEX CONCURRENTLY idx_bets_user_status_date 
ON bets (user_id, status, placed_at DESC);

CREATE INDEX CONCURRENTLY idx_events_sport_date 
ON events (sport_id, start_time) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY idx_odds_event_market 
ON odds (event_id, market_id, is_active) 
WHERE is_active = true;
```

### Connection Pooling
- API servers: 20 connections per instance
- Background jobs: 5 connections per worker
- Read replicas: 10 connections for reporting

### Caching Strategy
- **L1 Cache (Application)**: Odds data (5 second TTL)
- **L2 Cache (Redis)**: User sessions (24 hour TTL)
- **L3 Cache (CDN)**: Static assets (1 year TTL)

## API Optimization

### Rate Limiting
- Authenticated users: 1000 requests/hour
- Anonymous users: 100 requests/hour
- Bet placement: 10 bets/minute per user
- Odds requests: 60 requests/minute per user

### Response Optimization
- Gzip compression for all responses
- JSON minification
- Efficient serialization
- Pagination for large datasets

## Real-time Optimization

### WebSocket Scaling
- Sticky sessions for WebSocket connections
- Redis adapter for multi-instance scaling
- Connection pooling and reuse
- Efficient event broadcasting

### Odds Update Optimization
- Batch odds updates every 100ms
- Only send significant changes (>1% difference)
- Compress odds data before transmission
- Use binary protocols for high-frequency data
EOF
```

### Step 4: Development Environment Setup

#### 4.1 Docker Configuration
```bash
cd ../../infrastructure/docker

# Create development Docker Compose
cat > docker-compose.dev.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: betting_platform_dev
      POSTGRES_USER: betting_user
      POSTGRES_PASSWORD: betting_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: >
      redis-server
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000

  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis

volumes:
  postgres_data:
  redis_data:
EOF

# Create production-ready Dockerfile for API
cat > Dockerfile.api << 'EOF'
# Multi-stage build for production optimization
FROM node:18-alpine AS base
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies
FROM base AS deps
COPY package.json pnpm-lock.yaml* ./
RUN corepack enable pnpm && pnpm install --frozen-lockfile

# Build application
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN corepack enable pnpm && pnpm run build

# Production image
FROM base AS runner
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 betting-api

COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

USER betting-api

EXPOSE 3000

ENV NODE_ENV=production
ENV PORT=3000

CMD ["node", "dist/index.js"]
EOF
```

#### 4.2 Environment Configuration
```bash
# Create environment templates
cat > ../../.env.example << 'EOF'
# Database Configuration
DATABASE_URL="postgresql://betting_user:betting_password@localhost:5432/betting_platform_dev"
DATABASE_POOL_SIZE=20

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_POOL_SIZE=10

# JWT Configuration
JWT_ACCESS_SECRET="your-super-secret-jwt-access-key-change-in-production"
JWT_REFRESH_SECRET="your-super-secret-jwt-refresh-key-change-in-production"
JWT_ACCESS_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Encryption
ENCRYPTION_KEY="your-32-character-encryption-key-here"

# API Configuration
API_PORT=3000
API_HOST="localhost"
CORS_ORIGIN="http://localhost:3001"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL="debug"
LOG_FORMAT="combined"

# External APIs
SPORTS_DATA_API_KEY="your-sports-data-api-key"
ODDS_PROVIDER_API_KEY="your-odds-provider-api-key"

# Payment Providers
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_stripe_webhook_secret"

# Email Service
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
NEW_RELIC_LICENSE_KEY="your-new-relic-key"

# Feature Flags
ENABLE_LIVE_BETTING=true
ENABLE_CRYPTO_PAYMENTS=false
ENABLE_CASINO_GAMES=true
EOF

# Copy to actual .env file
cp .env.example .env
```

## Best Practices & Common Pitfalls

### ✅ Best Practices

1. **Database Design**:
   - Use UUIDs for primary keys to prevent enumeration attacks
   - Implement soft deletes for audit compliance
   - Use database constraints to enforce business rules
   - Partition large tables by date for better performance

2. **Security**:
   - Never store sensitive data in plain text
   - Implement proper input validation on all endpoints
   - Use parameterized queries to prevent SQL injection
   - Implement proper CORS policies

3. **Performance**:
   - Use connection pooling for database connections
   - Implement proper caching strategies
   - Use CDN for static assets
   - Optimize database queries with proper indexing

### ❌ Common Pitfalls to Avoid

1. **Architecture Mistakes**:
   - Don't create a monolithic application - use microservices
   - Don't ignore database transaction boundaries
   - Don't forget to implement proper error handling
   - Don't skip load testing during development

2. **Security Issues**:
   - Don't store passwords in plain text
   - Don't expose internal IDs in URLs
   - Don't trust client-side validation alone
   - Don't ignore rate limiting

3. **Performance Problems**:
   - Don't fetch unnecessary data from database
   - Don't ignore N+1 query problems
   - Don't forget to implement pagination
   - Don't skip database indexing

## Testing Strategy

### Unit Testing Setup
```bash
cd apps/api

# Create test configuration
cat > jest.config.js << 'EOF'
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/index.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
};
EOF

# Create test setup
mkdir -p src/test
cat > src/test/setup.ts << 'EOF'
import { PrismaClient } from '@prisma/client';

// Mock Prisma client for testing
jest.mock('@prisma/client');

// Setup test database
beforeAll(async () => {
  // Initialize test database
});

afterAll(async () => {
  // Cleanup test database
});

beforeEach(async () => {
  // Reset test data
});
EOF
```

This completes Phase 1 implementation. The foundation is now set for building a robust, scalable betting platform with proper architecture, security, and performance considerations.
