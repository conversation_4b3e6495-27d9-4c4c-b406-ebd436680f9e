{"$schema": "https://turbo.build/schema.json", "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "dist/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "lint": {"outputs": []}, "clean": {"cache": false}}, "globalDependencies": ["**/.env.*local"]}