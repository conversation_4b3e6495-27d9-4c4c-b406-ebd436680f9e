# Security & Compliance Guide

## Security Framework

### 1. Authentication & Authorization

#### Multi-Factor Authentication (MFA)
```typescript
// Implementation example for TOTP-based 2FA
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';

export class MFAService {
  static generateSecret(userEmail: string) {
    return speakeasy.generateSecret({
      name: `Betting Platform (${userEmail})`,
      issuer: 'Your Betting Platform'
    });
  }

  static generateQRCode(secret: string): Promise<string> {
    return QRCode.toDataURL(secret);
  }

  static verifyToken(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2
    });
  }
}
```

#### JWT Security Implementation
```typescript
// Secure JWT implementation with refresh tokens
export class AuthService {
  static generateTokens(userId: string) {
    const accessToken = jwt.sign(
      { userId, type: 'access' },
      process.env.JWT_ACCESS_SECRET!,
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      { userId, type: 'refresh' },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: '7d' }
    );

    return { accessToken, refreshToken };
  }

  static async revokeAllTokens(userId: string) {
    await prisma.userSession.deleteMany({
      where: { userId }
    });
  }
}
```

### 2. Data Protection & Encryption

#### Sensitive Data Encryption
```typescript
import crypto from 'crypto';

export class EncryptionService {
  private static algorithm = 'aes-256-gcm';
  private static key = Buffer.from(process.env.ENCRYPTION_KEY!, 'hex');

  static encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key);
    cipher.setAAD(Buffer.from('betting-platform'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  }

  static decrypt(encryptedData: string): string {
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const authTag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];
    
    const decipher = crypto.createDecipher(this.algorithm, this.key);
    decipher.setAAD(Buffer.from('betting-platform'));
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

### 3. Fraud Detection System

#### Machine Learning-based Fraud Detection
```typescript
export class FraudDetectionService {
  static async analyzeTransaction(transaction: Transaction, user: User): Promise<RiskScore> {
    const features = await this.extractFeatures(transaction, user);
    const riskScore = await this.calculateRiskScore(features);
    
    if (riskScore.score > 0.8) {
      await this.flagForReview(transaction, riskScore);
    }
    
    return riskScore;
  }

  private static async extractFeatures(transaction: Transaction, user: User) {
    return {
      transactionAmount: transaction.amount,
      userAge: this.calculateUserAge(user.createdAt),
      transactionFrequency: await this.getTransactionFrequency(user.id),
      deviceFingerprint: await this.getDeviceFingerprint(transaction),
      geolocation: await this.getGeolocation(transaction),
      timeOfDay: new Date().getHours(),
      dayOfWeek: new Date().getDay()
    };
  }

  private static async calculateRiskScore(features: any): Promise<RiskScore> {
    // Implement ML model or rule-based scoring
    let score = 0;
    
    // High amount transactions
    if (features.transactionAmount > 10000) score += 0.3;
    
    // New user
    if (features.userAge < 7) score += 0.2;
    
    // High frequency
    if (features.transactionFrequency > 10) score += 0.2;
    
    // Unusual time
    if (features.timeOfDay < 6 || features.timeOfDay > 23) score += 0.1;
    
    return {
      score: Math.min(score, 1),
      factors: this.identifyRiskFactors(features, score)
    };
  }
}
```

### 4. Responsible Gambling Implementation

#### Self-Exclusion System
```typescript
export class ResponsibleGamblingService {
  static async setSelfExclusion(userId: string, duration: number, type: 'TEMPORARY' | 'PERMANENT') {
    const expiresAt = type === 'PERMANENT' ? null : new Date(Date.now() + duration * 24 * 60 * 60 * 1000);
    
    await prisma.selfExclusion.create({
      data: {
        userId,
        type,
        expiresAt,
        isActive: true
      }
    });

    // Immediately close all active sessions
    await AuthService.revokeAllTokens(userId);
    
    // Send confirmation email
    await EmailService.sendSelfExclusionConfirmation(userId, type, expiresAt);
  }

  static async checkGamblingLimits(userId: string, betAmount: number): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { gamblingLimits: true }
    });

    if (!user?.gamblingLimits) return true;

    const limits = user.gamblingLimits;
    const now = new Date();
    
    // Check daily limit
    const dailySpent = await this.getDailySpent(userId, now);
    if (dailySpent + betAmount > limits.dailyLimit) return false;
    
    // Check weekly limit
    const weeklySpent = await this.getWeeklySpent(userId, now);
    if (weeklySpent + betAmount > limits.weeklyLimit) return false;
    
    // Check monthly limit
    const monthlySpent = await this.getMonthlySpent(userId, now);
    if (monthlySpent + betAmount > limits.monthlyLimit) return false;
    
    return true;
  }
}
```

## Compliance Framework

### 1. KYC/AML Implementation

#### Document Verification Service
```typescript
export class KYCService {
  static async initiateVerification(userId: string, documents: VerificationDocument[]) {
    const verification = await prisma.verification.create({
      data: {
        userId,
        type: 'IDENTITY',
        status: 'PENDING',
        documents: {
          create: documents.map(doc => ({
            type: doc.type,
            url: doc.url,
            metadata: doc.metadata
          }))
        }
      }
    });

    // Send to third-party verification service (e.g., Jumio, Onfido)
    await this.submitToVerificationProvider(verification);
    
    return verification;
  }

  static async processVerificationResult(verificationId: string, result: VerificationResult) {
    await prisma.verification.update({
      where: { id: verificationId },
      data: {
        status: result.status,
        confidence: result.confidence,
        notes: result.notes,
        completedAt: new Date()
      }
    });

    if (result.status === 'APPROVED') {
      await this.activateUserAccount(result.userId);
    } else if (result.status === 'REJECTED') {
      await this.handleRejectedVerification(result.userId, result.reasons);
    }
  }
}
```

#### AML Transaction Monitoring
```typescript
export class AMLService {
  static async monitorTransaction(transaction: Transaction) {
    const riskFactors = await this.analyzeTransaction(transaction);
    
    if (riskFactors.score > 0.7) {
      await this.createSuspiciousActivityReport(transaction, riskFactors);
    }
    
    // Check against sanctions lists
    const sanctionsCheck = await this.checkSanctionsList(transaction.userId);
    if (sanctionsCheck.isMatch) {
      await this.freezeAccount(transaction.userId, 'SANCTIONS_MATCH');
    }
  }

  private static async analyzeTransaction(transaction: Transaction): Promise<RiskAssessment> {
    const user = await prisma.user.findUnique({
      where: { id: transaction.userId },
      include: { transactions: true }
    });

    return {
      structuringRisk: this.checkStructuring(user.transactions),
      velocityRisk: this.checkVelocity(user.transactions),
      amountRisk: this.checkUnusualAmount(transaction, user.transactions),
      geographicRisk: await this.checkGeographicRisk(transaction),
      score: 0 // Calculate composite score
    };
  }
}
```

### 2. Regulatory Reporting

#### Automated Compliance Reporting
```typescript
export class ComplianceReportingService {
  static async generateDailyReport(date: Date) {
    const report = {
      date,
      totalDeposits: await this.getTotalDeposits(date),
      totalWithdrawals: await this.getTotalWithdrawals(date),
      totalBets: await this.getTotalBets(date),
      grossGamingRevenue: await this.getGGR(date),
      newRegistrations: await this.getNewRegistrations(date),
      suspiciousTransactions: await this.getSuspiciousTransactions(date),
      selfExclusions: await this.getSelfExclusions(date)
    };

    await this.submitToRegulator(report);
    return report;
  }

  static async generateSAR(transaction: Transaction, reasons: string[]) {
    const sar = {
      transactionId: transaction.id,
      userId: transaction.userId,
      amount: transaction.amount,
      currency: transaction.currency,
      suspiciousReasons: reasons,
      reportDate: new Date(),
      reportingOfficer: 'system'
    };

    await prisma.suspiciousActivityReport.create({ data: sar });
    await this.notifyComplianceTeam(sar);
  }
}
```

### 3. Data Privacy (GDPR/CCPA)

#### Data Subject Rights Implementation
```typescript
export class DataPrivacyService {
  static async handleDataSubjectRequest(userId: string, requestType: 'ACCESS' | 'DELETE' | 'PORTABILITY') {
    switch (requestType) {
      case 'ACCESS':
        return await this.generateDataExport(userId);
      
      case 'DELETE':
        return await this.processDataDeletion(userId);
      
      case 'PORTABILITY':
        return await this.generatePortableData(userId);
    }
  }

  private static async processDataDeletion(userId: string) {
    // Check if user can be deleted (no pending transactions, etc.)
    const canDelete = await this.checkDeletionEligibility(userId);
    
    if (!canDelete) {
      throw new Error('User cannot be deleted due to regulatory requirements');
    }

    // Anonymize instead of delete for audit trail
    await this.anonymizeUserData(userId);
  }

  private static async anonymizeUserData(userId: string) {
    const anonymizedData = {
      email: `deleted_${userId}@anonymized.com`,
      firstName: 'DELETED',
      lastName: 'USER',
      phoneNumber: null,
      dateOfBirth: null
    };

    await prisma.user.update({
      where: { id: userId },
      data: {
        ...anonymizedData,
        isActive: false,
        deletedAt: new Date()
      }
    });
  }
}
```

## Security Monitoring & Incident Response

### 1. Security Event Monitoring
```typescript
export class SecurityMonitoringService {
  static async logSecurityEvent(event: SecurityEvent) {
    await prisma.securityEvent.create({
      data: {
        type: event.type,
        severity: event.severity,
        userId: event.userId,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        details: event.details,
        timestamp: new Date()
      }
    });

    if (event.severity === 'HIGH' || event.severity === 'CRITICAL') {
      await this.triggerIncidentResponse(event);
    }
  }

  static async detectAnomalousActivity(userId: string) {
    const recentActivity = await this.getUserActivity(userId, 24); // Last 24 hours
    
    const anomalies = [
      this.detectUnusualLoginPattern(recentActivity),
      this.detectSuspiciousBetting(recentActivity),
      this.detectAccountTakeover(recentActivity)
    ].filter(Boolean);

    if (anomalies.length > 0) {
      await this.logSecurityEvent({
        type: 'ANOMALOUS_ACTIVITY',
        severity: 'MEDIUM',
        userId,
        details: { anomalies }
      });
    }
  }
}
```

### 2. Incident Response Automation
```typescript
export class IncidentResponseService {
  static async handleSecurityIncident(incident: SecurityIncident) {
    // Immediate response
    await this.executeImmediateResponse(incident);
    
    // Investigation
    const investigation = await this.initiateInvestigation(incident);
    
    // Notification
    await this.notifyStakeholders(incident, investigation);
    
    // Documentation
    await this.documentIncident(incident, investigation);
  }

  private static async executeImmediateResponse(incident: SecurityIncident) {
    switch (incident.type) {
      case 'ACCOUNT_COMPROMISE':
        await this.freezeAccount(incident.userId);
        await AuthService.revokeAllTokens(incident.userId);
        break;
        
      case 'PAYMENT_FRAUD':
        await this.suspendPaymentMethods(incident.userId);
        break;
        
      case 'DATA_BREACH':
        await this.activateBreachProtocol();
        break;
    }
  }
}
```

This security and compliance framework provides comprehensive protection for your betting platform while ensuring regulatory compliance across multiple jurisdictions.
